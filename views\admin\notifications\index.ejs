<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-bell"></i> إشعارات الإدارة</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                            <i class="fas fa-check-double"></i> تحديد الكل كمقروء
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="clearAllNotifications()">
                            <i class="fas fa-trash"></i> مسح الكل
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <% if (notifications.length === 0) { %>
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle"></i> لا توجد إشعارات بعد.
                            </div>
                        <% } %>
                        <% notifications.forEach(notification => { %>
                            <div class="list-group-item <%= notification.readAt ? '' : 'list-group-item-warning' %>" data-notification-id="<%= notification.id %>">
                                <div class="d-flex w-100 justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0 fw-bold"><%= notification.title %></h6>
                                            <div>
                                                <% if (!notification.readAt) { %>
                                                    <span class="badge bg-warning text-dark me-2">جديد</span>
                                                <% } %>
                                                <small class="text-muted"><%= new Date(notification.createdAt).toLocaleString('ar-SA') %></small>
                                            </div>
                                        </div>
                                        <p class="mb-2 text-muted"><%= notification.message %></p>
                                        <% if (notification.actionUrl) { %>
                                            <a href="<%= notification.actionUrl %>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> <%= notification.actionText || 'عرض التفاصيل' %>
                                            </a>
                                        <% } %>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <% if (!notification.readAt) { %>
                                                <li><a class="dropdown-item" href="#" onclick="markAsRead('<%= notification.id %>')">
                                                    <i class="fas fa-check"></i> تحديد كمقروء
                                                </a></li>
                                            <% } %>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteNotification('<%= notification.id %>')">
                                                <i class="fas fa-trash"></i> حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-paper-plane"></i> إرسال إشعار مخصص</h5>
                </div>
                <div class="card-body">
                    <form id="customNotificationForm">
                        <div class="mb-3">
                            <label for="notificationTitle" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        <div class="mb-3">
                            <label for="notificationMessage" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="notificationMessage" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="notificationTarget" class="form-label">المستهدفون</label>
                            <select class="form-select" id="notificationTarget">
                                <option value="all_admins">جميع المدراء</option>
                                <option value="all_customers">جميع العملاء</option>
                                <option value="all">الجميع</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notificationPriority" class="form-label">الأولوية</label>
                            <select class="form-select" id="notificationPriority">
                                <option value="normal">عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-paper-plane"></i> إرسال الإشعار
                        </button>
                    </form>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> إعدادات الإشعارات</h5>
                </div>
                <div class="card-body">
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                        <label class="form-check-label" for="enableNotifications">
                            تفعيل الإشعارات
                        </label>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="enableSounds" checked>
                        <label class="form-check-label" for="enableSounds">
                            تفعيل الأصوات
                        </label>
                    </div>
                    <button class="btn btn-outline-primary btn-sm w-100" onclick="testNotification()">
                        <i class="fas fa-test-tube"></i> اختبار الإشعارات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <% if (totalPages > 1) { %>
        <div class="row mt-4">
            <div class="col-md-8">
                <nav>
                    <ul class="pagination justify-content-center">
                        <% for (let i = 1; i <= totalPages; i++) { %>
                            <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                                <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                            </li>
                        <% } %>
                    </ul>
                </nav>
            </div>
        </div>
    <% } %>
</div>

<script>
// إدارة الإشعارات
async function markAsRead(notificationId) {
    try {
        const response = await fetch(`/admin/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('list-group-item-warning');
            notificationElement.querySelector('.badge')?.remove();
            showToast('تم تحديد الإشعار كمقروء', 'success');
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
        showToast('حدث خطأ أثناء تحديث الإشعار', 'error');
    }
}

async function deleteNotification(notificationId) {
    if (!confirm('هل أنت متأكد من حذف هذا الإشعار؟')) return;

    try {
        const response = await fetch(`/admin/notifications/${notificationId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            document.querySelector(`[data-notification-id="${notificationId}"]`).remove();
            showToast('تم حذف الإشعار بنجاح', 'success');
        }
    } catch (error) {
        console.error('Error deleting notification:', error);
        showToast('حدث خطأ أثناء حذف الإشعار', 'error');
    }
}

async function markAllAsRead() {
    try {
        const response = await fetch('/admin/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            document.querySelectorAll('.list-group-item-warning').forEach(item => {
                item.classList.remove('list-group-item-warning');
                item.querySelector('.badge')?.remove();
            });
            showToast('تم تحديد جميع الإشعارات كمقروءة', 'success');
        }
    } catch (error) {
        console.error('Error marking all as read:', error);
        showToast('حدث خطأ أثناء تحديث الإشعارات', 'error');
    }
}

async function clearAllNotifications() {
    if (!confirm('هل أنت متأكد من حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

    try {
        const response = await fetch('/admin/notifications/clear-all', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        if (response.ok) {
            location.reload();
        }
    } catch (error) {
        console.error('Error clearing notifications:', error);
        showToast('حدث خطأ أثناء حذف الإشعارات', 'error');
    }
}

// إرسال إشعار مخصص
document.getElementById('customNotificationForm').addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = {
        title: document.getElementById('notificationTitle').value,
        message: document.getElementById('notificationMessage').value,
        target: document.getElementById('notificationTarget').value,
        priority: document.getElementById('notificationPriority').value
    };

    try {
        const response = await fetch('/admin/notifications/send-custom', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (response.ok) {
            showToast(`تم إرسال الإشعار بنجاح إلى ${result.sentCount} مستخدم`, 'success');
            document.getElementById('customNotificationForm').reset();
        } else {
            showToast(result.message || 'حدث خطأ أثناء إرسال الإشعار', 'error');
        }
    } catch (error) {
        console.error('Error sending custom notification:', error);
        showToast('حدث خطأ أثناء إرسال الإشعار', 'error');
    }
});

// اختبار الإشعارات
async function testNotification() {
    try {
        const response = await fetch('/admin/test-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (response.ok) {
            showToast('تم إرسال إشعار تجريبي بنجاح', 'success');
        } else {
            showToast(result.message || 'حدث خطأ أثناء إرسال الإشعار التجريبي', 'error');
        }
    } catch (error) {
        console.error('Error sending test notification:', error);
        showToast('حدث خطأ أثناء إرسال الإشعار التجريبي', 'error');
    }
}

// عرض رسائل التنبيه
function showToast(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 5000);
}
</script>