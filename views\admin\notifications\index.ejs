<div class="container mt-4">
    <h2 class="mb-4"><i class="fas fa-bell"></i> إشعارات الإدارة</h2>
    <div class="list-group">
        <% if (notifications.length === 0) { %>
            <div class="alert alert-info">لا توجد إشعارات بعد.</div>
        <% } %>
        <% notifications.forEach(notification => { %>
            <a href="/notifications/<%= notification.id %>" class="list-group-item list-group-item-action <%= notification.readAt ? '' : 'list-group-item-warning' %>">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><%= notification.title %></h5>
                    <small><%= new Date(notification.createdAt).toLocaleString('ar-SA') %></small>
                </div>
                <p class="mb-1"><%= notification.message %></p>
                <% if (!notification.readAt) { %>
                    <span class="badge bg-warning text-dark">جديد</span>
                <% } %>
            </a>
        <% }); %>
    </div>
    <!-- Pagination -->
    <% if (totalPages > 1) { %>
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                <% for (let i = 1; i <= totalPages; i++) { %>
                    <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>
</div>