<div class="container-fluid mt-4">
  <div class="row">
    <!-- Notifications Panel -->
    <div class="col-md-8">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-bell"></i> إشعارات الإدارة
          </h5>
          <div>
            <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
              <i class="fas fa-check-double"></i> تحديد الكل كمقروء
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="clearAllNotifications()">
              <i class="fas fa-trash"></i> مسح الكل
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="list-group list-group-flush">
            <% if (notifications.length === 0) { %>
              <div class="alert alert-info mb-0">
                <i class="fas fa-info-circle"></i> لا توجد إشعارات بعد.
              </div>
            <% } %>
            <% notifications.forEach(notification => { 
                try {
                  notification.data = JSON.parse(notification.data);
                } catch(e) {
                  notification.data = {};
                }
            %>
            <div 
            class="list-group-item list-group-item-action mb-2 <%= notification.readAt ? '' : 'list-group-item-warning' %>" 
            data-notification-id="<%= notification.id %>" 
            onclick="location.href='/admin/orders/<%= notification.data.orderId %>'"
            style="cursor: pointer;"
            >
                <div class="d-flex justify-content-between align-items-start w-100">
                    <div class="flex-grow-1">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0 fw-bold"><%= notification.title %></h6>
                        <div>
                        <% if (!notification.readAt) { %>
                            <span class="badge bg-warning text-dark me-2">جديد</span>
                        <% } %>
                        <small class="text-muted">
                            <%= new Date(notification.createdAt).toLocaleString('ar-SA') %>
                        </small>
                        </div>
                    </div>
                    <p class="mb-2 text-muted"><%= notification.message %></p>
                    <% if (notification.actionUrl) { %>
                        <a 
                        href="<%= notification.actionUrl %>" 
                        class="btn btn-sm btn-outline-primary" 
                        onclick="event.stopPropagation();"
                        >
                        <i class="fas fa-external-link-alt"></i> <%= notification.actionText || 'عرض التفاصيل' %>
                        </a>
                    <% } %>
                    </div>
                    <div class="dropdown">
                    <button 
                        class="btn btn-sm btn-outline-secondary" 
                        type="button" 
                        data-bs-toggle="dropdown"
                        onclick="event.stopPropagation();"
                    >
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <% if (!notification.readAt) { %>
                        <li>
                            <a 
                            class="dropdown-item" 
                            href="#" 
                            onclick="event.stopPropagation(); markAsRead('<%= notification.id %>')"
                            >
                            <i class="fas fa-check"></i> تحديد كمقروء
                            </a>
                        </li>
                        <% } %>
                        <li>
                        <a 
                            class="dropdown-item text-danger" 
                            href="#" 
                            onclick="event.stopPropagation(); deleteNotification('<%= notification.id %>')"
                        >
                            <i class="fas fa-trash"></i> حذف
                        </a>
                        </li>
                    </ul>
                    </div>
                </div>
            </div>

            <% }); %>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications Settings -->
    <div class="col-md-4">
      <div class="card mt-3">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-cog"></i> إعدادات الإشعارات
          </h5>
        </div>
        <div class="card-body">
          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
            <label class="form-check-label" for="enableNotifications">تفعيل الإشعارات</label>
          </div>
          <div class="form-check form-switch mb-3">
            <input class="form-check-input" type="checkbox" id="enableSounds" checked>
            <label class="form-check-label" for="enableSounds">تفعيل الأصوات</label>
          </div>
          <button class="btn btn-outline-primary btn-sm w-100" onclick="testNotification()">
            <i class="fas fa-test-tube"></i> اختبار الإشعارات
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <% if (totalPages > 1) { %>
    <div class="row mt-4">
      <div class="col-md-8">
        <nav>
          <ul class="pagination justify-content-center">
            <% for (let i = 1; i <= totalPages; i++) { %>
              <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                <a class="page-link" href="?page=<%= i %>"><%= i %></a>
              </li>
            <% } %>
          </ul>
        </nav>
      </div>
    </div>
  <% } %>
</div>

<script src="/js/notification-actions.js"></script>
