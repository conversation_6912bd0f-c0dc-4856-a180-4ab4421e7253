const { Order, Delivery, Customer, Delivery<PERSON>erson, DeliveryPeopleArea, OrderDetail, Product } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const NotificationService = require('../services/NotificationService');

class DeliveryController {

  // عرض توصيلات سائق معين داخل متجر
  async personDeliveries(req, res) {
    const { personId } = req.params;

    try {
      const inProgressDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['pending', 'in_transit']
        },
        include: [
          {
            model: Order,
            as: 'order',
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const completedDeliveries = await Delivery.findAll({
        where: {
          deliveryPersonId: personId,
          status: ['delivered', 'cancelled']
        },
        include: [
          {
            model: Order,
            as: 'order',
            include: ['customer']
          },
          { model: DeliveryPerson, as: 'courier' }
        ],
        order: [['createdAt', 'DESC']]
      });

      const person = await DeliveryPerson.findByPk(personId, { attributes: ['name'] });
      const deliveryPersonName = person ? person.name : null;

      res.render('admin/delivery/index', {
        personId,
        deliveryPersonName,
        inProgressDeliveries,
        completedDeliveries
      });

    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Server error' } });
    }
  }
  
  // عرض كل التوصيلات الخاصة بالمتجر (dashboard)
  async index(req, res) {
    try {
      const currentPage = parseInt(req.query.page) || 1;
      const limit = 20;
      const offset = (currentPage - 1) * limit;
  
      const { count, rows: deliveries } = await Delivery.findAndCountAll({
        include: [
          {
            model: Order,
            as: 'order',
            include: [{ model: Customer, as: 'customer' }]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });
  
      const totalPages = Math.ceil(count / limit);
  
      res.render('admin/delivery/dashboard', {
        deliveries,
        currentPage,
        totalPages
      });
  
    } catch (error) {
      console.error('Error fetching deliveries:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch deliveries' } });
    }
  }
  
  // عرض صفحة تفاصيل التوصيل لطلب معين
  async show(req, res) {
    const orderId = req.params.id;
    const order = await Order.findByPk(orderId, {
      include: [
        { model: Delivery, as: 'delivery',
          include: [{ model: DeliveryPerson, as: 'courier' }]
        },
        { model: Customer, as: 'customer' },
        
      ]
    });
    if (!order) return res.status(404).render('error', { error: { message: 'Order not found' } });
    res.render('admin/delivery/show', { order, delivery: order.delivery });
  }

 // عرض نموذج إنشاء توصيل جديد (مع اختيار سائق تلقائي)
async createForm(req, res) {
  const deliveryPersonId = req.query.deliveryPersonId || null;
  const orderId = req.query.orderId;
  const showAll = req.query.showAll === 'on';

  try {
      const pendingOrders = await Order.findAll({
      where: {
        status: {
          [Op.in]: ['pending', 'processing']
        }
      },
      include: [{ model: Customer, as: 'customer' }]
    });

    let deliveryPeople = [];

    if (showAll) {
      // عرض جميع السائقين النشطين
      deliveryPeople = await DeliveryPerson.findAll({
        where: { status: 'active' },
        include: [{ model: DeliveryPeopleArea, as: 'areas' }]
      });
    } else if (pendingOrders.length > 0) {
      // عرض فقط السائقين في نفس مدينة الزبون
      const order = pendingOrders[0];

      deliveryPeople = await DeliveryPerson.findAll({
        where: { status: 'active' },
        include: [{
          model: DeliveryPeopleArea,
          as: 'areas',
          where: {
            city: order.customer.city
          }
        }]
      });
    }

    res.render('admin/delivery/create', {
      deliveryPeople,
      pendingOrders,
      selectedDeliveryPersonId: deliveryPersonId,
      showAll,
      errors: null,
      formData: {orderId}
    });

  } catch (error) {
    console.error(error);
    res.status(500).send('Server error');
  }
}



  // معالجة إنشاء توصيل جديد من نموذج
  async create(req, res) {
    const { deliveryPersonId, orderId, status, pickupTime, deliveryTime, notes } = req.body;

    try {
    const existingDelivery = await Delivery.findOne({ where: { orderId } });
    if (existingDelivery) {
      // إذا موجود، ارجع رسالة خطأ أو إعادة توجيه مع رسالة
      return res.status(400).render('error', {
        error: 'هذا الطلب لديه توصيل موجود مسبقاً.',
        orderId
      });
    }
      const delivery = await Delivery.create({
        orderId: orderId || null,
        deliveryPersonId: deliveryPersonId || null,
        status,
        pickupTime: pickupTime || null,
        deliveryTime: deliveryTime || null,
        notes: notes || null,
      });
      await Order.update({ status: 'out_for_delivery' }, { where: { id: orderId } });

      //this.sendNewDeliveryNotifications(delivery);
    

      res.redirect(`/admin/deliveries`);
    } catch (error) {
      console.error(error);
      const deliveryPeople = await DeliveryPerson.findAll();
      res.render('admin/deliveries/create', {
        deliveryPeople,
        selectedDeliveryPersonId: deliveryPersonId,
        errors: error.errors,
        formData: req.body
      });
    }
  }
 async sendNewDeliveryNotifications(delivery) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const deliveryWithDetails = await Delivery.findByPk(delivery.id, {
                include: [
                    {
                        model: Order,
                        as: 'order',
                        include: [{ model: Customer, as: 'customer' },
                          { model: OrderDetail, as: 'orderDetails' 
                            ,include: [{
                            model: Product,
                            as: 'product'
                          }]
                          },
                        ]
                    },
                    {
                        model: DeliveryPerson,
                        as: 'courier'
                        
                    }
                ]
            });

            if (!deliveryWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(deliveryWithDetails.order.customer.id, {
                title: 'تم قبول طلبك ',
                message: `تم قبول طلبك رقم #${deliveryWithDetails.order.id} بنجاح بإجمالي ${deliveryWithDetails.order.totalPrice} ليرة.`,
                type: 'success',
                priority: 'normal',
                customerId: deliveryWithDetails.order.customer.id,
                actionUrl: `/customers/orders/${deliveryWithDetails.order.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: deliveryWithDetails.order.id,
                    totalPrice: deliveryWithDetails.order.totalPrice,
                    type: 'order_created'
                }
            });

            // إرسال إشعار للمدراء
            await NotificationService.notifyDriver({
                title: 'توصيل طلب',
                message: `تم اسناد توصيل جديد للطلب رقم #${deliveryWithDetails.order.id} من العميل ${deliveryWithDetails.order.customer.name}\nإجمالي المبلغ: ${deliveryWithDetails.order.totalPrice} ليرة\n`,
                type: 'delivery',
                priority: 'normal',
                customerId: deliveryWithDetails.order.customer.id,
                actionUrl: `/admin/orders/${deliveryWithDetails.order.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: deliveryWithDetails.order.id,
                    customerId: deliveryWithDetails.order.customer.id,
                    customerName: deliveryWithDetails.order.customer.name,
                    totalPrice: deliveryWithDetails.order.totalPrice,
                    type: 'new_order_admin'
                }
            });

            logger.info(`Order notifications sent for order ${deliveryWithDetails.order.id} to customer ${deliveryWithDetails.order.customer.id}`);
            // إرسال إشعارات للمديرين باستخدام الخدمة المحسنة
             // إرسال إشعار للمدراء عن التوصيل الجديد
      try {
        const FirebaseMessagingService = require('../services/FirebaseMessagingService');

        // جلب بيانات الطلب والسائق
        const order = await Order.findByPk(orderId, {
          include: [{ model: Customer, as: 'customer', attributes: ['name'] }]
        });
        const driver = await DeliveryPerson.findByPk(deliveryPersonId, {
          attributes: ['name']
        });

        const notification = {
          title: 'توصيل جديد',
          body: `تم إنشاء توصيل جديد للطلب #${orderId} - السائق: ${driver?.name || 'غير محدد'}`
        };

        const data = {
          deliveryId: delivery.id.toString(),
          orderId: orderId.toString(),
          driverId: deliveryPersonId?.toString() || '',
          driverName: driver?.name || 'غير محدد',
              customerName: order?.customer?.name || 'غير محدد',
              clickAction: `/admin/deliveries/${delivery.id}`,
              type: 'new_delivery'
            };

            const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
          } catch (notificationError) {
            console.error('❌ Error sending delivery notification:', notificationError);
            // لا نوقف العملية إذا فشل الإشعار
          }
                        
            } catch (error) {
                logger.error('Error in sendNewOrderNotifications:', error);
                throw error;
            }
        }

  // عرض نموذج تعديل توصيل
  async editForm(req, res) {
    const delivery = await Delivery.findByPk(req.params.id, { include: ['order'] });
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });
    res.render('admin/delivery/edit', { delivery });
  }

  // تحديث بيانات توصيل
  async update(req, res) {
    const { status, pickupTime, deliveryTime, notes } = req.body;
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.update({ status, pickupTime, deliveryTime, notes });
    res.redirect('/admin/deliveries');
  }

  // حذف توصيل
  async delete(req, res) {
    const delivery = await Delivery.findByPk(req.params.id);
    if (!delivery) return res.status(404).render('error', { error: { message: 'Delivery not found' } });

    await delivery.destroy();
    res.redirect('/admin/drivers/'+req.params.id+'/deliveries');
  }

  // تحديث حالة التوصيل (مثلاً من صفحة خاصة)
  async markComplete(req, res) {
    const { deliveryId } = req.params;
    try {
      // جلب التوصيل مع الطلب المرتبط للتأكد من صلاحية المتجر
      const delivery = await Delivery.findOne({
        where: { id: deliveryId },
        include: {
          model: Order,
          as: 'order',
        }
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      // تحديث حالة التوصيل
      delivery.status = 'delivered'; // أو حسب الحالة التي تستخدمها
      await delivery.save();
  
      // تحديث حالة الطلب المرتبط
     /* delivery.order.status = 'delivered'; // حسب النظام لديك
      await delivery.order.save();*/

      res.redirect('/admin/drivers'+deliveryId+'/deliveries');
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to update delivery status' } });
    }
  }

  async showDelivery(req, res) {
    const { orderId } = req.params;
  
    try {
      // جلب التوصيل المرتبط بالطلب مع بيانات السائق والطلب والعميل (حسب الربط)
      const delivery = await Delivery.findOne({
        where: { orderId },
        include: [
          {
            model: Order,
            as: 'order',
            include: [{ model: Customer, as: 'customer' }]
          },
          {
            model: DeliveryPerson, // اسم الموديل للسائق عندك
            as: 'courier'          // تأكد من alias في associations
          }
        ]
      });
  
      if (!delivery) {
        return res.status(404).render('error', { error: { message: 'Delivery not found' } });
      }
  
      res.render('admin/delivery/show', { delivery });
    } catch (error) {
      console.error(error);
      res.status(500).render('error', { error: { message: 'Unable to fetch delivery details' } });
    }
  }
  
}

module.exports = new DeliveryController();
