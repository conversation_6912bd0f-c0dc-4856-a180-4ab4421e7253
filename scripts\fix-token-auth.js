#!/usr/bin/env node

/**
 * حل شامل لمشكلة مصادقة التوكن
 * يتعامل مع خطأ: messaging/token-subscribe-failed
 */

require('dotenv').config();
const admin = require('firebase-admin');
const { serviceAccountPath, firebaseConfig, vapidKey } = require('../config/firebase');

async function fixTokenAuth() {
  console.log('🔧 حل مشكلة مصادقة التوكن...\n');

  try {
    // 1. تهيئة Firebase Admin SDK
    if (admin.apps.length === 0) {
      const serviceAccount = require(`../${serviceAccountPath}`);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    }

    // 2. فحص إعدادات Web Push في Firebase
    console.log('🌐 فحص إعدادات Web Push...');
    
    // محاولة إنشاء رسالة تجريبية للتحقق من إعدادات Web Push
    const testMessage = {
      notification: {
        title: 'اختبار إعدادات Web Push',
        body: 'هذا اختبار للتأكد من صحة الإعدادات'
      },
      webpush: {
        notification: {
          icon: '/logo.png'
        }
      },
      token: 'dummy-token-for-validation'
    };

    try {
      await admin.messaging().send(testMessage);
    } catch (error) {
      if (error.code === 'messaging/invalid-registration-token') {
        console.log('✅ إعدادات Web Push صحيحة (خطأ متوقع للتوكن التجريبي)');
      } else if (error.code === 'messaging/third-party-auth-error') {
        console.error('❌ مشكلة في إعدادات Web Push في Firebase Console');
        console.log('\n🔧 الحلول المطلوبة:');
        console.log('   1. اذهب إلى Firebase Console');
        console.log('   2. Project Settings > Cloud Messaging');
        console.log('   3. تأكد من تفعيل Cloud Messaging API');
        console.log('   4. تحقق من صحة مفتاح VAPID');
        return false;
      } else {
        console.log(`ℹ️ استجابة غير متوقعة: ${error.code}`);
      }
    }

    // 3. إنشاء ملف تكوين محدث للعميل
    console.log('\n📝 إنشاء ملف تكوين محدث...');
    
    const clientConfig = `
// تكوين Firebase محدث - تم إنشاؤه تلقائياً
window.FIREBASE_CONFIG = ${JSON.stringify(firebaseConfig, null, 2)};
window.VAPID_KEY = '${vapidKey}';

// دالة مساعدة لتهيئة Firebase
window.initializeFirebaseMessaging = function() {
  if (!firebase || !firebase.messaging) {
    throw new Error('Firebase Messaging غير محمل');
  }
  
  firebase.initializeApp(window.FIREBASE_CONFIG);
  return firebase.messaging();
};

// دالة لحل مشاكل التوكن
window.fixTokenIssues = async function() {
  try {
    const messaging = window.initializeFirebaseMessaging();
    
    // حذف التوكن القديم
    try {
      await messaging.deleteToken();
      console.log('🗑️ تم حذف التوكن القديم');
    } catch (e) {
      console.log('ℹ️ لا يوجد توكن قديم للحذف');
    }
    
    // إعادة تسجيل Service Worker
    const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
      scope: '/',
      updateViaCache: 'none'
    });
    
    await navigator.serviceWorker.ready;
    
    // الحصول على توكن جديد
    const token = await messaging.getToken({
      vapidKey: window.VAPID_KEY,
      serviceWorkerRegistration: registration
    });
    
    return token;
  } catch (error) {
    console.error('خطأ في إصلاح التوكن:', error);
    throw error;
  }
};

console.log('✅ تم تحميل إعدادات Firebase المحدثة');
`;

    require('fs').writeFileSync('./public/firebase-config.js', clientConfig);
    console.log('✅ تم إنشاء ملف firebase-config.js');

    // 4. إنشاء صفحة اختبار
    const testPage = `
<!DOCTYPE html>
<html>
<head>
    <title>اختبار إشعارات Firebase</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>اختبار إشعارات Firebase</h1>
    <button id="testBtn">اختبار التوكن</button>
    <div id="result"></div>
    
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>
    <script src="/firebase-config.js"></script>
    
    <script>
        document.getElementById('testBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'جاري الاختبار...';
            
            try {
                // طلب إذن الإشعارات
                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    throw new Error('تم رفض إذن الإشعارات');
                }
                
                // إصلاح مشاكل التوكن
                const token = await window.fixTokenIssues();
                
                if (token) {
                    resultDiv.innerHTML = \`
                        <h3>✅ نجح الاختبار!</h3>
                        <p><strong>التوكن:</strong> \${token.substring(0, 50)}...</p>
                        <p>يمكنك الآن استخدام هذا التوكن في التطبيق</p>
                    \`;
                } else {
                    throw new Error('فشل في الحصول على التوكن');
                }
            } catch (error) {
                resultDiv.innerHTML = \`
                    <h3>❌ فشل الاختبار</h3>
                    <p><strong>الخطأ:</strong> \${error.message}</p>
                    <p>راجع Console للمزيد من التفاصيل</p>
                \`;
                console.error('خطأ في الاختبار:', error);
            }
        });
    </script>
</body>
</html>
`;

    require('fs').writeFileSync('./public/test-notifications.html', testPage);
    console.log('✅ تم إنشاء صفحة اختبار: /test-notifications.html');

    console.log('\n🎯 الخطوات التالية:');
    console.log('   1. أعد تشغيل الخادم');
    console.log('   2. امسح cache المتصفح (Ctrl+Shift+Delete)');
    console.log('   3. اذهب إلى: http://localhost:3000/test-notifications.html');
    console.log('   4. اضغط على "اختبار التوكن"');
    console.log('   5. إذا نجح الاختبار، جرب التفعيل في لوحة التحكم');

    return true;

  } catch (error) {
    console.error('\n❌ فشل في حل المشكلة:', error.message);
    return false;
  }
}

// تشغيل الحل
if (require.main === module) {
  fixTokenAuth()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('خطأ غير متوقع:', error);
      process.exit(1);
    });
}

module.exports = fixTokenAuth;
