const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/AdminController');
const CategoryController = require('../controllers/CategoryController');
const CustomersController = require('../controllers/CustomersController');
const ProductsController = require('../controllers/ProductsController');
const OrdersController = require('../controllers/OrdersController');
const deliveryController = require('../controllers/DeliveryController');
const driversController = require('../controllers/DriversController');
const notificationsController = require('../controllers/NotificationsController');


const { requireAdminAuth } = require('./admin-auth');

// إعداد multer لرفع الصور
const { uploadProducts, uploadCategories, uploadCustomers,uploadDeliveries, handleMulterError } = require('../middleware/upload');

// Apply authentication middleware to all admin routes
router.use(requireAdminAuth);

// Dashboard route
router.get('/', (req, res) => {
  res.redirect('/admin/dashboard');
});

router.get('/dashboard', AdminController.dashboard.bind(AdminController));

// Categories routes
router.get('/categories', CategoryController.index.bind(CategoryController));
router.get('/categories/create', CategoryController.create.bind(CategoryController));
router.post('/categories',uploadCategories.single('image'), CategoryController.insert.bind(CategoryController));
router.get('/categories/:id', CategoryController.show.bind(CategoryController));
router.get('/categories/:id/edit', CategoryController.edit.bind(CategoryController));
router.post('/categories/:id', uploadCategories.single('image'), CategoryController.update.bind(CategoryController));
router.post('/categories/:id/delete', CategoryController.delete.bind(CategoryController));

// Products routes
router.get('/products', ProductsController.index.bind(ProductsController));
router.get('/products/create', ProductsController.create.bind(ProductsController));
router.post('/products', uploadProducts.array('images', 10), ProductsController.insert.bind(ProductsController));
router.get('/products/:id', ProductsController.show.bind(ProductsController));
router.get('/products/:id/edit', ProductsController.edit.bind(ProductsController));
router.post('/products/:id', uploadProducts.array('images', 10), ProductsController.update.bind(ProductsController));
router.post('/products/:id/delete', ProductsController.destroy.bind(ProductsController));
router.post('/products/images/:id/delete', ProductsController.deleteImage.bind(ProductsController));
router.get('/products/:id/change-image', ProductsController.addoffer.bind(ProductsController));
router.post('/products/:id/change-image', uploadProducts.single('image'), ProductsController.addoffer_image.bind(ProductsController));
router.post('/products/:id/delete-image', ProductsController.deleteoffer_image.bind(ProductsController));

router.get('/offers', ProductsController.showoffers.bind(ProductsController));

// Customers routes
router.get('/customers', CustomersController.index.bind(CustomersController));
router.get('/customers/create', CustomersController.create.bind(CustomersController));
router.post('/customers', uploadCustomers.single('image'), CustomersController.insert.bind(CustomersController));
router.get('/customers/:id', CustomersController.show.bind(CustomersController));
router.get('/customers/:id/edit', CustomersController.edit.bind(CustomersController));
router.post('/customers/:id', uploadCustomers.single('image'), CustomersController.update.bind(CustomersController));
router.post('/customers/:id/delete', CustomersController.delete.bind(CustomersController));
router.post('/customers/:id/status', CustomersController.updateStatus.bind(CustomersController));

// Orders routes
router.get('/orders', OrdersController.index.bind(OrdersController));
router.get('/orders/pending', OrdersController.pendingOrders.bind(OrdersController));
router.get('/orders/:id', OrdersController.show.bind(OrdersController));
router.post('/orders/:id/approve', OrdersController.approve.bind(OrdersController));
router.post('/orders/:id/reject', OrdersController.reject.bind(OrdersController));
router.post('/orders/:id/assign-delivery', OrdersController.assignDelivery.bind(OrdersController));
router.post('/orders/:id/status', OrdersController.updateStatus.bind(OrdersController));

// Deliveries routes
router.get('/deliveries', deliveryController.index.bind(deliveryController));
router.get('/deliveries/create', deliveryController.createForm.bind(deliveryController));
router.get('/deliveries/:id', deliveryController.show.bind(deliveryController));
router.post('/deliveries', deliveryController.create.bind(deliveryController));
router.get('/deliveries/:id/edit', deliveryController.editForm.bind(deliveryController));
router.post('/deliveries/:id',deliveryController.update.bind(deliveryController));
router.post('/deliveries/:id/delete', deliveryController.delete.bind(deliveryController));

// Drivers routes
router.get('/drivers', driversController.index.bind(driversController));
router.get('/drivers/create', driversController.createForm.bind(driversController));
router.post('/drivers',uploadDeliveries.single('image'), driversController.create.bind(driversController));
router.get('/drivers/:id/edit', driversController.editForm.bind(driversController));
router.post('/drivers/:id',uploadDeliveries.single('image'), driversController.update.bind(driversController));
router.post('/drivers/:id/delete', driversController.delete.bind(driversController));
router.get('/drivers/:personId/deliveries', deliveryController.personDeliveries.bind(deliveryController));
router.post('/deliveries/:deliveryId/complete', deliveryController.markComplete.bind(deliveryController));
router.delete('/deliveries/:id/delete', deliveryController.delete.bind(deliveryController));

router.post('/savetoken', AdminController.AdminToken.bind(AdminController));
router.post('/test-notification', AdminController.testNotification.bind(AdminController));

// طرق إدارة الإشعارات
router.get('/notifications', notificationsController.adminIndex.bind(notificationsController));
router.post('/notifications/send-custom', AdminController.sendCustomNotification.bind(AdminController));
router.post('/notifications/:id/read', AdminController.markNotificationAsRead.bind(AdminController));
router.delete('/notifications/:id', AdminController.deleteNotification.bind(AdminController));
router.post('/notifications/mark-all-read', AdminController.markAllNotificationsAsRead.bind(AdminController));
router.delete('/notifications/clear-all', AdminController.clearAllNotifications.bind(AdminController));

module.exports = router;
