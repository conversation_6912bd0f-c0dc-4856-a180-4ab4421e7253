#!/usr/bin/env node

/**
 * أداة لتحديث مفتاح VAPID في جميع الملفات
 * استخدم هذه الأداة عند تغيير مفتاح VAPID في Firebase Console
 */

const fs = require('fs');
const path = require('path');

// المفتاح الجديد - ضع هنا المفتاح من Firebase Console
const NEW_VAPID_KEY = 'YOUR_NEW_VAPID_KEY_HERE';

// المفتاح القديم الحالي
const OLD_VAPID_KEY = 'BAeiibK5g-rcijaiIb-kC1qWVzeLEWWIvYsbfj6xHx162ETMsPF8lPuq9mRRVkvBn6NMSMnZYRYtP4-5sbe8vRk';

// الملفات التي تحتوي على مفتاح VAPID
const FILES_TO_UPDATE = [
  'config/firebase.js',
  'views/admin/dashboard.ejs',
  'public/firebase-messaging-sw.js'
];

function updateVapidKey() {
  console.log('🔄 بدء تحديث مفتاح VAPID...\n');

  if (NEW_VAPID_KEY === 'YOUR_NEW_VAPID_KEY_HERE') {
    console.error('❌ يرجى تحديث المفتاح الجديد في السكريبت أولاً');
    console.log('💡 احصل على المفتاح من: https://console.firebase.google.com');
    console.log('   Project Settings > Cloud Messaging > Web configuration');
    return;
  }

  if (NEW_VAPID_KEY.length < 80) {
    console.error('❌ مفتاح VAPID قصير جداً. تأكد من نسخ المفتاح كاملاً');
    return;
  }

  let updatedFiles = 0;
  let errors = 0;

  FILES_TO_UPDATE.forEach(filePath => {
    try {
      const fullPath = path.join(__dirname, '..', filePath);
      
      if (!fs.existsSync(fullPath)) {
        console.warn(`⚠️ الملف غير موجود: ${filePath}`);
        return;
      }

      let content = fs.readFileSync(fullPath, 'utf8');
      
      if (content.includes(OLD_VAPID_KEY)) {
        content = content.replace(new RegExp(OLD_VAPID_KEY, 'g'), NEW_VAPID_KEY);
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ تم تحديث: ${filePath}`);
        updatedFiles++;
      } else {
        console.log(`ℹ️ لا يحتاج تحديث: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ خطأ في تحديث ${filePath}:`, error.message);
      errors++;
    }
  });

  console.log(`\n📊 النتائج:`);
  console.log(`   ✅ تم تحديث: ${updatedFiles} ملف`);
  console.log(`   ❌ أخطاء: ${errors}`);

  if (updatedFiles > 0) {
    console.log('\n🔄 خطوات ما بعد التحديث:');
    console.log('   1. أعد تشغيل الخادم');
    console.log('   2. امسح cache المتصفح');
    console.log('   3. اختبر الإشعارات: node scripts/test-firebase-notifications.js');
  }
}

// عرض المفتاح الحالي
function showCurrentKey() {
  console.log('🔑 المفتاح الحالي:');
  console.log(`   ${OLD_VAPID_KEY}`);
  console.log(`   الطول: ${OLD_VAPID_KEY.length} حرف`);
}

// فحص مفتاح VAPID في Firebase Console
function checkVapidKey() {
  console.log('🔍 فحص مفتاح VAPID...\n');
  
  const configPath = path.join(__dirname, '..', 'config/firebase.js');
  
  if (!fs.existsSync(configPath)) {
    console.error('❌ ملف الإعدادات غير موجود');
    return;
  }

  try {
    const config = require(configPath);
    const vapidKey = config.vapidKey;
    
    if (!vapidKey) {
      console.error('❌ مفتاح VAPID غير موجود في الإعدادات');
      return;
    }

    console.log('✅ مفتاح VAPID موجود');
    console.log(`   الطول: ${vapidKey.length} حرف`);
    console.log(`   البداية: ${vapidKey.substring(0, 20)}...`);
    console.log(`   النهاية: ...${vapidKey.substring(vapidKey.length - 20)}`);

    if (vapidKey.length < 80) {
      console.warn('⚠️ المفتاح قصير جداً - قد يكون غير صحيح');
    }

    if (vapidKey === OLD_VAPID_KEY) {
      console.log('ℹ️ هذا هو المفتاح الافتراضي - قد تحتاج لتحديثه');
    }

  } catch (error) {
    console.error('❌ خطأ في قراءة الإعدادات:', error.message);
  }
}

// تشغيل الأداة
if (require.main === module) {
  const command = process.argv[2];

  switch (command) {
    case 'update':
      updateVapidKey();
      break;
    case 'show':
      showCurrentKey();
      break;
    case 'check':
      checkVapidKey();
      break;
    default:
      console.log('🔧 أداة تحديث مفتاح VAPID\n');
      console.log('الاستخدام:');
      console.log('  node scripts/update-vapid-key.js check   - فحص المفتاح الحالي');
      console.log('  node scripts/update-vapid-key.js show    - عرض المفتاح الحالي');
      console.log('  node scripts/update-vapid-key.js update  - تحديث المفتاح');
      console.log('\nقبل التحديث:');
      console.log('  1. احصل على المفتاح الجديد من Firebase Console');
      console.log('  2. حدث المتغير NEW_VAPID_KEY في هذا الملف');
      console.log('  3. شغل الأمر update');
  }
}

module.exports = { updateVapidKey, showCurrentKey, checkVapidKey };
