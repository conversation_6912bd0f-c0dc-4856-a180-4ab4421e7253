const express = require('express');
const router = express.Router();
const notificationsController = require('../controllers/NotificationsController');

// API Routes (يجب أن تكون قبل Web Routes لتجنب التضارب)
router.get('/api/unread-count', notificationsController.getUnreadCount.bind(notificationsController));
router.get('/api/unread', notificationsController.getUnreadCount.bind(notificationsController));
router.get('/api/user-notifications', notificationsController.getUserNotifications.bind(notificationsController));

// Web Routes للإشعارات
router.get('/', notificationsController.index.bind(notificationsController));
router.get('/create', notificationsController.create.bind(notificationsController));
router.get('/unread-count', notificationsController.getUnreadCount.bind(notificationsController));
router.get('/unread', notificationsController.getUnreadCount.bind(notificationsController));
router.get('/user-notifications', notificationsController.getUserNotifications.bind(notificationsController));
router.post('/', notificationsController.insert.bind(notificationsController));
router.post('/mark-all-read', notificationsController.markAllAsRead.bind(notificationsController));

// Routes مع معاملات (يجب أن تكون في النهاية)
router.get('/:id', notificationsController.show.bind(notificationsController));
router.post('/:id/read', notificationsController.markAsRead.bind(notificationsController));
router.post('/:id/delete', notificationsController.delete.bind(notificationsController));

module.exports = router;
