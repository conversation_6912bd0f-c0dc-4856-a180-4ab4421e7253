const { sequelize } = require('../models');

async function checkCustomersTable() {
    try {
        console.log('فحص بنية جدول العملاء...');
        
        // الحصول على معلومات الأعمدة
        const [results] = await sequelize.query(`
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Customers'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('\nالأعمدة الموجودة في جدول العملاء:');
        console.log('=====================================');
        results.forEach(column => {
            console.log(`${column.COLUMN_NAME} - ${column.DATA_TYPE} - ${column.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
        });
        
        console.log('\n✅ تم فحص الجدول بنجاح');
        
    } catch (error) {
        console.error('❌ خطأ في فحص الجدول:', error.message);
    } finally {
        await sequelize.close();
    }
}

// تشغيل الدالة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    checkCustomersTable().then(() => {
        process.exit(0);
    }).catch(error => {
        console.error(error);
        process.exit(1);
    });
}

module.exports = checkCustomersTable;
