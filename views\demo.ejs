<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض النظام الجديد - نظام إدارة المتاجر الذكي</title>
    
    <!-- خط Cairo للعربية -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS الموحد -->
    <link href="/css/main.css" rel="stylesheet">
</head>
<body>
    <!-- الشريط العلوي -->
    <nav class="navbar">
        <div class="navbar-container">
            <a href="#" class="navbar-brand">
                <div class="navbar-brand-icon">
                    <i class="fas fa-store"></i>
                </div>
                <span>نظام إدارة المتاجر الذكي</span>
            </a>
            
            <ul class="navbar-nav">
                <li class="navbar-nav-item">
                    <a href="#" class="navbar-nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </li>
                <li class="navbar-nav-item navbar-notification">
                    <a href="#" class="navbar-nav-link">
                        <i class="fas fa-bell"></i>
                        الإشعارات
                        <span class="navbar-notification-badge">3</span>
                    </a>
                </li>
                <li class="navbar-nav-item navbar-dropdown">
                    <a href="#" class="navbar-nav-link">
                        <i class="fas fa-user"></i>
                        حسابي
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container mt-6">
        <!-- العنوان الرئيسي -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-primary mb-4">
                <i class="fas fa-palette me-3"></i>
                نظام إدارة المتاجر الذكي
            </h1>
            <p class="lead text-secondary">
                تصميم احترافي متكامل بألوان متناسقة من درجات الأخضر
            </p>
        </div>

        <!-- كروت الإحصائيات -->
        <div class="row g-4 mb-8">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="stats-card-value">245</div>
                    <div class="stats-card-label">المتاجر المسجلة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stats-card-value">1,234</div>
                    <div class="stats-card-label">الطلبات اليوم</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-card-value">5,678</div>
                    <div class="stats-card-label">العملاء النشطين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stats-card-value">89%</div>
                    <div class="stats-card-label">معدل النمو</div>
                </div>
            </div>
        </div>

        <!-- الأزرار -->
        <div class="row mb-8">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">مجموعة الأزرار</h3>
                    </div>
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-3 mb-4">
                            <button class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                إضافة جديد
                            </button>
                            <button class="btn btn-success">
                                <i class="fas fa-check me-2"></i>
                                حفظ
                            </button>
                            <button class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>
                                تعديل
                            </button>
                            <button class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>
                                حذف
                            </button>
                            <button class="btn btn-info">
                                <i class="fas fa-info me-2"></i>
                                معلومات
                            </button>
                        </div>
                        
                        <div class="d-flex flex-wrap gap-3 mb-4">
                            <button class="btn btn-outline-primary">
                                <i class="fas fa-download me-2"></i>
                                تحميل
                            </button>
                            <button class="btn btn-outline-secondary">
                                <i class="fas fa-print me-2"></i>
                                طباعة
                            </button>
                            <button class="btn btn-ghost">
                                <i class="fas fa-share me-2"></i>
                                مشاركة
                            </button>
                        </div>

                        <div class="d-flex flex-wrap gap-3 mb-4">
                            <button class="btn btn-primary btn-sm">صغير</button>
                            <button class="btn btn-primary">عادي</button>
                            <button class="btn btn-primary btn-lg">كبير</button>
                            <button class="btn btn-primary btn-xl">كبير جداً</button>
                        </div>

                        <h5>اختبار نظام الإشعارات المحسن</h5>
                        <div class="d-flex flex-wrap gap-3">
                            <button class="btn btn-success btn-sm test-notification"
                                    data-type="success"
                                    data-message="تم الحفظ بنجاح!">
                                <i class="fas fa-check me-2"></i>
                                إشعار نجاح
                            </button>
                            <button class="btn btn-warning btn-sm test-notification"
                                    data-type="warning"
                                    data-message="يرجى مراجعة البيانات">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                إشعار تحذير
                            </button>
                            <button class="btn btn-danger btn-sm test-notification"
                                    data-type="danger"
                                    data-message="حدث خطأ في النظام">
                                <i class="fas fa-times me-2"></i>
                                إشعار خطأ
                            </button>
                            <button class="btn btn-info btn-sm test-notification"
                                    data-type="info"
                                    data-message="معلومة مهمة للمستخدم">
                                <i class="fas fa-info me-2"></i>
                                إشعار معلومات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التنبيهات -->
        <div class="row mb-8">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">التنبيهات</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <div class="alert-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">نجح!</div>
                                تم حفظ البيانات بنجاح.
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <div class="alert-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">تحذير!</div>
                                يرجى مراجعة البيانات المدخلة.
                            </div>
                        </div>

                        <div class="alert alert-danger">
                            <div class="alert-icon">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">خطأ!</div>
                                حدث خطأ أثناء معالجة الطلب.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <div class="alert-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="alert-content">
                                <div class="alert-title">معلومة</div>
                                هذه رسالة إعلامية مهمة.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- النماذج -->
        <div class="row mb-8">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">نموذج تسجيل</h3>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="form-group">
                                <label class="form-label required">الاسم الكامل</label>
                                <input type="text" class="form-control" placeholder="أدخل اسمك الكامل">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label required">البريد الإلكتروني</label>
                                <input type="email" class="form-control" placeholder="<EMAIL>">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">نوع المستخدم</label>
                                <select class="form-control form-select">
                                    <option>اختر نوع المستخدم</option>
                                    <option>عميل</option>
                                    <option>صاحب متجر</option>
                                    <option>مدير</option>
                                </select>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="terms">
                                <label class="form-check-label" for="terms">
                                    أوافق على الشروط والأحكام
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-user-plus me-2"></i>
                                تسجيل
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">البحث والفلترة</h3>
                    </div>
                    <div class="card-body">
                        <div class="input-group mb-4">
                            <input type="text" class="form-control" placeholder="ابحث عن المنتجات...">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select class="form-control form-select">
                                <option>جميع الفئات</option>
                                <option>إلكترونيات</option>
                                <option>ملابس</option>
                                <option>كتب</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">نطاق السعر</label>
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" placeholder="من">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" placeholder="إلى">
                                </div>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary btn-sm">
                            <i class="fas fa-filter me-2"></i>
                            تطبيق الفلتر
                        </button>
                        <button class="btn btn-secondary btn-sm">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>
    <script>
        // تأثيرات بسيطة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات الحركة
            const cards = document.querySelectorAll('.card, .stats-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in');
            });

            // اختبار نظام الإشعارات المحسن
            setTimeout(() => {
                SmartStore.showNotification('مرحباً بك في النظام الجديد!', 'success', 5000, {
                    title: 'أهلاً وسهلاً',
                    sound: false
                });
            }, 2000);

            setTimeout(() => {
                SmartStore.showNotification('تم تحديث النظام بنجاح', 'info', 4000);
            }, 4000);

            // إضافة أزرار اختبار الإشعارات
            const testButtons = document.querySelectorAll('.test-notification');
            testButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const type = this.dataset.type || 'info';
                    const message = this.dataset.message || 'رسالة تجريبية';
                    SmartStore.showNotification(message, type);
                });
            });
        });
    </script>
</body>
</html>
