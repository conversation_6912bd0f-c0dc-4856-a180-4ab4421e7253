<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="connect-src 'self' https://nominatim.openstreetmap.org https://www.googleapis.com http://firebaseinstallations.googleapis.com https://fcmregistrations.googleapis.com;">

    <title>لوحة الإدارة - نظام إدارة المتجر الذكي</title>

    <!-- Meta Tags -->
    <meta name="description" content="لوحة إدارة نظام المتجر الذكي">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/css/fixed-main.css" rel="stylesheet">
    <link href="/css/admin.css" rel="stylesheet">

    <!-- Notification Styles -->
    <style>
        :root {
            --primary-color: #B2CD9C;
            --secondary-color: #8FBC8F;
            --accent-color: #7BA05B;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white) !important;
        }

        .container-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .action-card h5 {
            color: var(--text-dark);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 0.5rem;
            width: 100%;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-lg);
            border-radius: 10px;
        }

        .dropdown-item:hover {
            background: var(--bg-light);
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    
        .notification-status-indicator {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
            background-color: #dc3545; /* Red by default (disabled) */
            transition: background-color 0.3s ease;
        }

        .notification-status-indicator.enabled {
            background-color: #28a745; /* Green when enabled */
        }

        .notification-status-indicator.pending {
            background-color: #ffc107; /* Yellow when pending */
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            animation: bounce 0.5s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-5px); }
            60% { transform: translateY(-3px); }
        }

        .alert-sm {
            padding: 0.5rem;
            font-size: 0.875rem;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .notification-item:hover {
            background-color: #f8f9fa;
        }

        .notification-item.unread {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #6c757d;
        }

        .notification-status-dot {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #dc3545;
            transition: background-color 0.3s ease;
        }

        .notification-status-dot.enabled {
            background-color: #28a745;
        }

        .notification-status-dot.pending {
            background-color: #ffc107;
            animation: pulse 1.5s infinite;
        }
    </style>
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Firebase for Notifications -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>
    <script src="/firebase-config.js"></script>
</head>
<body>
    <!-- Sidebar Toggle Button (Mobile) -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

     <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                     <li class="nav-item">
                        <a class="nav-link" href="/admin/offers">
                            <i class="fas fa-tags me-1"></i>العروض
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-box-open me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders/pending">
                            <i class="fas fa-clock me-1"></i>الطلبات المعلقة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/drivers">
                            <i class="fas fa-truck me-1"></i>السائقين
                        </a>
                    </li>
                      <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                    <li class="nav-item position-relative">
                        <a class="nav-link" href="/admin/notifications">
                            <i class="fas fa-bell me-1"></i>الإشعارات
                            <span class="notification-status-dot" id="sidebarNotificationDot"></span>
                        </a>
                    </li>
                </ul>

                <!-- Notifications Dropdown -->
                <div class="dropdown me-3">
                    <button class="btn btn-outline-light btn-sm position-relative" type="button" data-bs-toggle="dropdown" id="notificationDropdown">
                        <i class="fas fa-bell"></i>
                        <span class="notification-status-indicator" id="notificationStatusDot"></span>
                        <span class="notification-badge d-none" id="notificationBadge">0</span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end" style="width: 350px; max-height: 400px; overflow-y: auto;">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>الإشعارات</span>
                            <small class="text-muted" id="notificationStatus">جاري التحميل...</small>
                        </div>
                        <div class="dropdown-divider"></div>

                        <!-- Permission Alert -->
                        <div class="alert alert-warning alert-sm m-2 d-none" id="permissionAlert">
                            <i class="fas fa-exclamation-triangle"></i>
                            يرجى السماح بالإشعارات لتلقي التحديثات
                            <button class="btn btn-sm btn-warning mt-1" onclick="window.adminNotifications.requestPermission()">
                                تفعيل الإشعارات
                            </button>
                        </div>

                        <!-- Notifications List -->
                        <div id="notificationsList">
                            <div class="text-center p-3">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                <div class="mt-2">جاري تحميل الإشعارات...</div>
                            </div>
                        </div>

                        <div class="dropdown-divider"></div>
                        <div class="dropdown-item-text text-center">
                            <a href="/admin/notifications" class="btn btn-sm btn-primary">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </div>
                </div>

                <div class="dropdown">
                    <button class="btn toggle-btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                         المدير
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/notifications">
                            <i class="fas fa-bell me-2"></i>الإشعارات
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    <div id="admin-notifications" style="position: fixed; top: 200px; right: 10px; z-index: 1000;"></div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="/js/admin.js"></script>

    <!-- Page-specific scripts -->
    <% if (locals.pageScripts) { %>
        <%- pageScripts %>
    <% } %>
    
    <script>
    document.querySelector('.toggle-btn').addEventListener('click', function() {
        this.nextElementSibling.classList.toggle('show');
    });
    </script>

<!-- 2. كود تهيئة Firebase والرسائل -->
<script>
  const firebaseConfig = {
    apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
    authDomain: "company-firebase-77daf.firebaseapp.com",
    projectId: "company-firebase-77daf",
    storageBucket: "company-firebase-77daf.appspot.com",
    messagingSenderId: "76415949840",
    appId: "1:76415949840:web:d74b24b187a1abf392fe95",
    measurementId: "G-YRPEE91G0C"
  };

  // تهيئة Firebase
  let messaging;
  try {
    messaging = window.initializeFirebaseMessaging();
    console.log("✅ تم تهيئة Firebase Messaging بنجاح");
  } catch (initError) {
    console.error("❌ فشل في تهيئة Firebase:", initError);
    firebase.initializeApp(firebaseConfig);
    messaging = firebase.messaging();
  }

  window.addEventListener('load', async () => {
    try {
      console.log("🔄 بدء تفعيل الإشعارات التلقائي...");

      // فحص دعم المتصفح
      if (!('Notification' in window)) {
        throw new Error('هذا المتصفح لا يدعم الإشعارات');
      }

      if (!('serviceWorker' in navigator)) {
        throw new Error('هذا المتصفح لا يدعم Service Workers');
      }

      if (!firebase || !firebase.messaging) {
        throw new Error('Firebase Messaging غير محمل بشكل صحيح');
      }

      console.log("✅ دعم المتصفح جاهز");

      // طلب الإذن مباشرة
      const permission = await Notification.requestPermission();
      if (permission !== 'granted') {
        throw new Error('تم رفض إذن الإشعارات. يرجى السماح بها من إعدادات المتصفح.');
      }

      console.log("✅ تم منح إذن الإشعارات");

      // تسجيل Service Worker
      let registration;
      try {
        const existingRegistrations = await navigator.serviceWorker.getRegistrations();
        for (const reg of existingRegistrations) {
          if (reg.scope.includes('firebase-messaging-sw')) {
            await reg.unregister();
            console.log("🗑️ تم إلغاء تسجيل Service Worker قديم");
          }
        }

        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
          scope: '/',
          updateViaCache: 'none'
        });

        console.log("✅ تم تسجيل Service Worker بنجاح");
        await navigator.serviceWorker.ready;

      } catch (swError) {
        console.error("❌ فشل تسجيل Service Worker:", swError);
        throw new Error(`فشل تسجيل Service Worker: ${swError.message}`);
      }

      // الحصول على FCM Token
      let currentToken;
      try {
        currentToken = await messaging.getToken({
          vapidKey: window.VAPID_KEY || 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc',
          serviceWorkerRegistration: registration
        });
      } catch (tokenError) {
        console.error("❌ فشل في توليد التوكن:", tokenError);
        throw new Error('فشل في توليد التوكن');
      }

      if (!currentToken) {
        throw new Error('لم يتم الحصول على توكن صالح');
      }

      console.log("✅ FCM Token:", currentToken.substring(0, 20) + "...");

      // حفظ التوكن بالسيرفر
      const response = await fetch('/admin/savetoken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: currentToken })
      });

      if (!response.ok) {
        throw new Error(`فشل في حفظ التوكن بالسيرفر (${response.status})`);
      }

      console.log("✅ تم حفظ التوكن بنجاح");

    } catch (error) {
      console.error("❌ حدث خطأ أثناء تفعيل الإشعارات:", error);
    }
  });

  // استقبال الإشعارات داخل الصفحة
  messaging.onMessage((payload) => {
    console.log('📥 إشعار وارد داخل الصفحة:', payload);

    const notification = document.createElement('div');
    notification.classList.add('alert', 'alert-info', 'alert-dismissible', 'fade', 'show');
    notification.innerHTML = `
      <strong>${payload.notification.title}</strong><br>
      ${payload.notification.body}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.getElementById('admin-notifications').appendChild(notification);
  });
</script>
    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('adminSidebar').classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('adminSidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 1024 && 
                !sidebar.contains(event.target) && 
                !toggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 300);
                }
            });
        }, 5000);
    </script>

    <!-- Firebase Notifications Script -->
    <script>
        // Global notification system for admin panel
        class AdminNotificationSystem {
            constructor() {
                this.messaging = null;
                this.isInitialized = false;
                this.notificationCount = 0;
                this.notifications = [];
                this.init();
            }

            async init() {
                try {
                    console.log('🔔 تهيئة نظام الإشعارات للإدمن...');

                    // Check if Firebase is loaded
                    if (typeof firebase === 'undefined') {
                        console.error('❌ Firebase غير محمل');
                        this.updateNotificationStatus('error');
                        return;
                    }

                    // Initialize Firebase messaging
                    if (window.initializeFirebaseMessaging) {
                        this.messaging = window.initializeFirebaseMessaging();
                    } else {
                        // Fallback
                        firebase.initializeApp(window.FIREBASE_CONFIG || {
                            apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
                            authDomain: "company-firebase-77daf.firebaseapp.com",
                            projectId: "company-firebase-77daf",
                            storageBucket: "company-firebase-77daf.appspot.com",
                            messagingSenderId: "76415949840",
                            appId: "1:76415949840:web:d74b24b187a1abf392fe95"
                        });
                        this.messaging = firebase.messaging();
                    }

                    // Check notification permission
                    await this.checkNotificationPermission();

                    // Setup message handlers
                    this.setupMessageHandlers();

                    // Setup UI event handlers
                    this.setupUIHandlers();

                    this.isInitialized = true;
                    console.log('✅ تم تهيئة نظام الإشعارات بنجاح');

                } catch (error) {
                    console.error('❌ فشل في تهيئة نظام الإشعارات:', error);
                    this.updateNotificationStatus('error');
                }
            }

            async checkNotificationPermission() {
                if (!('Notification' in window)) {
                    console.warn('⚠️ المتصفح لا يدعم الإشعارات');
                    this.updateNotificationStatus('unsupported');
                    return;
                }

                const permission = Notification.permission;
                console.log('🔐 حالة إذن الإشعارات:', permission);

                switch (permission) {
                    case 'granted':
                        await this.enableNotifications();
                        break;
                    case 'denied':
                        this.updateNotificationStatus('denied');
                        this.showPermissionAlert();
                        break;
                    case 'default':
                        this.updateNotificationStatus('default');
                        this.showPermissionAlert();
                        break;
                }
            }

            async enableNotifications() {
                try {
                    this.updateNotificationStatus('pending');

                    let token;
                    if (window.fixTokenIssues) {
                        token = await window.fixTokenIssues();
                    } else {
                        // Fallback method
                        const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                        await navigator.serviceWorker.ready;

                        token = await this.messaging.getToken({
                            vapidKey: window.VAPID_KEY || 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc',
                            serviceWorkerRegistration: registration
                        });
                    }

                    if (token) {
                        await this.saveTokenToServer(token);
                        this.updateNotificationStatus('enabled');
                        this.hidePermissionAlert();
                        console.log('✅ تم تفعيل الإشعارات بنجاح');

                        // Show success notification
                        this.showLocalNotification('تم تفعيل الإشعارات', 'سيتم إشعارك بالأحداث المهمة');
                    } else {
                        throw new Error('فشل في الحصول على التوكن');
                    }
                } catch (error) {
                    console.error('❌ فشل في تفعيل الإشعارات:', error);
                    this.updateNotificationStatus('error');
                    this.showError('فشل في تفعيل الإشعارات: ' + error.message);
                }
            }

            async saveTokenToServer(token) {
                const response = await fetch('/admin/savetoken', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ token })
                });

                if (!response.ok) {
                    throw new Error('فشل في حفظ التوكن على الخادم');
                }

                const result = await response.json();
                if (!result.success) {
                    throw new Error(result.message || 'فشل في حفظ التوكن');
                }
            }

            setupMessageHandlers() {
                // Handle foreground messages
                this.messaging.onMessage((payload) => {
                    console.log('📨 تم استلام إشعار:', payload);
                    this.handleIncomingNotification(payload);
                });

                // Handle token refresh
                this.messaging.onTokenRefresh(async () => {
                    console.log('🔄 تحديث التوكن...');
                    try {
                        const newToken = await this.messaging.getToken();
                        await this.saveTokenToServer(newToken);
                        console.log('✅ تم تحديث التوكن بنجاح');
                    } catch (error) {
                        console.error('❌ فشل في تحديث التوكن:', error);
                    }
                });
            }

            setupUIHandlers() {
                // Enable notifications button
                const enableBtn = document.getElementById('enableNotificationsBtn');
                const enableLink = document.getElementById('enableNotificationsLink');

                if (enableBtn) {
                    enableBtn.addEventListener('click', () => this.requestPermissionAndEnable());
                }

                if (enableLink) {
                    enableLink.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.requestPermissionAndEnable();
                    });
                }
            }

            async requestPermissionAndEnable() {
                try {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        await this.enableNotifications();
                    } else {
                        this.updateNotificationStatus('denied');
                        this.showError('تم رفض إذن الإشعارات');
                    }
                } catch (error) {
                    console.error('❌ فشل في طلب إذن الإشعارات:', error);
                    this.showError('فشل في طلب إذن الإشعارات');
                }
            }

            handleIncomingNotification(payload) {
                const notification = {
                    id: Date.now(),
                    title: payload.notification?.title || 'إشعار جديد',
                    body: payload.notification?.body || '',
                    icon: payload.notification?.icon || '/logo.png',
                    timestamp: new Date(),
                    data: payload.data || {},
                    unread: true
                };

                this.notifications.unshift(notification);
                this.notificationCount++;
                this.updateNotificationUI();

                // Show browser notification if page is not visible
                if (document.hidden) {
                    this.showLocalNotification(notification.title, notification.body, notification.icon);
                }
            }

            showLocalNotification(title, body, icon = '/logo.png') {
                if (Notification.permission === 'granted') {
                    new Notification(title, {
                        body: body,
                        icon: icon,
                        badge: '/logo.png',
                        tag: 'admin-notification'
                    });
                }
            }

            updateNotificationStatus(status) {
                const indicator = document.getElementById('notificationStatus');
                const sidebarDot = document.getElementById('sidebarNotificationStatus');

                // Update header indicator
                if (indicator) {
                    indicator.className = 'notification-status-indicator';

                    switch (status) {
                        case 'enabled':
                            indicator.classList.add('enabled');
                            indicator.title = 'الإشعارات مفعلة';
                            break;
                        case 'pending':
                            indicator.classList.add('pending');
                            indicator.title = 'جاري تفعيل الإشعارات...';
                            break;
                        case 'denied':
                        case 'error':
                            indicator.title = 'الإشعارات غير مفعلة';
                            break;
                        case 'unsupported':
                            indicator.title = 'المتصفح لا يدعم الإشعارات';
                            break;
                        default:
                            indicator.title = 'حالة الإشعارات غير معروفة';
                    }
                }

                // Update sidebar dot
                if (sidebarDot) {
                    sidebarDot.className = 'notification-status-dot';

                    switch (status) {
                        case 'enabled':
                            sidebarDot.classList.add('enabled');
                            break;
                        case 'pending':
                            sidebarDot.classList.add('pending');
                            break;
                    }
                }
            }

            showPermissionAlert() {
                const alert = document.getElementById('notificationPermissionAlert');
                const enableBtn = document.getElementById('enableNotificationsBtn');

                if (alert) alert.style.display = 'block';
                if (enableBtn) enableBtn.style.display = 'inline-block';
            }

            hidePermissionAlert() {
                const alert = document.getElementById('notificationPermissionAlert');
                const enableBtn = document.getElementById('enableNotificationsBtn');

                if (alert) alert.style.display = 'none';
                if (enableBtn) enableBtn.style.display = 'none';
            }

            updateNotificationUI() {
                const badge = document.getElementById('notificationBadge');
                const statusDot = document.getElementById('notificationStatusDot');
                const sidebarDot = document.getElementById('sidebarNotificationDot');
                const statusText = document.getElementById('notificationStatus');
                const list = document.getElementById('notificationsList');

                // Update badge
                if (badge) {
                    if (this.notificationCount > 0) {
                        badge.textContent = this.notificationCount > 99 ? '99+' : this.notificationCount.toString();
                        badge.classList.remove('d-none');
                    } else {
                        badge.classList.add('d-none');
                    }
                }

                // Update main notification status dot
                if (statusDot) {
                    statusDot.className = 'notification-status-indicator';
                    if (this.isEnabled) {
                        statusDot.classList.add('enabled');
                    } else if (this.isPending) {
                        statusDot.classList.add('pending');
                    }
                }

                // Update sidebar notification status dot
                if (sidebarDot) {
                    sidebarDot.className = 'notification-status-dot';
                    if (this.isEnabled) {
                        sidebarDot.classList.add('enabled');
                    } else if (this.isPending) {
                        sidebarDot.classList.add('pending');
                    }
                }

                // Update dashboard notification count if present
                const dashboardCount = document.getElementById('dashboardNotificationCount');
                if (dashboardCount) {
                    dashboardCount.textContent = this.notificationCount || 0;
                }

                // Update any other notification counters on the page
                const allNotificationCounters = document.querySelectorAll('[data-notification-counter]');
                allNotificationCounters.forEach(counter => {
                    counter.textContent = this.notificationCount || 0;
                });

                // Update status text
                if (statusText) {
                    if (this.isEnabled) {
                        statusText.textContent = 'مفعل';
                    } else if (this.isPending) {
                        statusText.textContent = 'انتظار...';
                    } else {
                        statusText.textContent = 'معطل';
                    }
                }

                // Update notification list
                if (list) {
                    if (this.notifications.length === 0) {
                        list.innerHTML = '<div class="dropdown-item text-muted text-center">لا توجد إشعارات جديدة</div>';
                    } else {
                        list.innerHTML = this.notifications.slice(0, 5).map(notification => `
                            <div class="notification-item ${notification.unread ? 'unread' : ''}" onclick="window.adminNotifications.markAsRead(${notification.id})">
                                <div class="d-flex">
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">${notification.title}</div>
                                        <div class="text-muted small">${notification.body}</div>
                                        <div class="notification-time">${this.formatTime(notification.timestamp)}</div>
                                    </div>
                                </div>
                            </div>
                        `).join('');
                    }
                }
            }

            markAsRead(notificationId) {
                const notification = this.notifications.find(n => n.id === notificationId);
                if (notification && notification.unread) {
                    notification.unread = false;
                    this.notificationCount = Math.max(0, this.notificationCount - 1);
                    this.updateNotificationUI();
                }
            }

            formatTime(date) {
                const now = new Date();
                const diff = now - date;
                const minutes = Math.floor(diff / 60000);

                if (minutes < 1) return 'الآن';
                if (minutes < 60) return `منذ ${minutes} دقيقة`;

                const hours = Math.floor(minutes / 60);
                if (hours < 24) return `منذ ${hours} ساعة`;

                const days = Math.floor(hours / 24);
                return `منذ ${days} يوم`;
            }

            showError(message) {
                // Create a temporary alert
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
                alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
                alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                document.body.appendChild(alertDiv);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }

        // Initialize notification system when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            try {
                window.adminNotifications = new AdminNotificationSystem();
                console.log('✅ تم تهيئة نظام الإشعارات بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تهيئة نظام الإشعارات:', error);
            }
        });
    </script>
</body>
</html>
