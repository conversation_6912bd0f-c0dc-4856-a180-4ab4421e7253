const express = require('express');
const router = express.Router();
const { sequelize } = require('../models');
const os = require('os');
const fs = require('fs').promises;
const path = require('path');

/**
 * نقطة فحص صحة النظام
 * GET /health
 */
router.get('/', async (req, res) => {
    try {
        const healthCheck = {
            status: 'OK',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0',
            services: {},
            system: {},
            performance: {}
        };

        // فحص قاعدة البيانات
        try {
            await sequelize.authenticate();
            healthCheck.services.database = {
                status: 'OK',
                type: sequelize.getDialect(),
                host: sequelize.config.host,
                database: sequelize.config.database
            };
        } catch (error) {
            healthCheck.services.database = {
                status: 'ERROR',
                error: error.message
            };
            healthCheck.status = 'DEGRADED';
        }

        // فحص Redis (إذا كان متاحاً)
        if (process.env.REDIS_URL) {
            try {
                // سيتم إضافة فحص Redis هنا لاحقاً
                healthCheck.services.redis = {
                    status: 'OK',
                    url: process.env.REDIS_URL.replace(/\/\/.*@/, '//***@') // إخفاء كلمة المرور
                };
            } catch (error) {
                healthCheck.services.redis = {
                    status: 'ERROR',
                    error: error.message
                };
                healthCheck.status = 'DEGRADED';
            }
        }

        // معلومات النظام
        healthCheck.system = {
            platform: os.platform(),
            arch: os.arch(),
            nodeVersion: process.version,
            totalMemory: `${Math.round(os.totalmem() / 1024 / 1024)} MB`,
            freeMemory: `${Math.round(os.freemem() / 1024 / 1024)} MB`,
            loadAverage: os.loadavg(),
            cpuCount: os.cpus().length
        };

        // معلومات الأداء
        const memUsage = process.memoryUsage();
        healthCheck.performance = {
            memoryUsage: {
                rss: `${Math.round(memUsage.rss / 1024 / 1024)} MB`,
                heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)} MB`,
                heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)} MB`,
                external: `${Math.round(memUsage.external / 1024 / 1024)} MB`
            },
            uptime: `${Math.floor(process.uptime())} seconds`,
            pid: process.pid
        };

        // فحص المجلدات المطلوبة
        const requiredDirs = ['uploads', 'logs', 'temp'];
        healthCheck.filesystem = {};

        for (const dir of requiredDirs) {
            try {
                await fs.access(dir);
                const stats = await fs.stat(dir);
                healthCheck.filesystem[dir] = {
                    status: 'OK',
                    exists: true,
                    isDirectory: stats.isDirectory(),
                    size: stats.size
                };
            } catch (error) {
                healthCheck.filesystem[dir] = {
                    status: 'ERROR',
                    exists: false,
                    error: error.message
                };
                if (healthCheck.status === 'OK') {
                    healthCheck.status = 'DEGRADED';
                }
            }
        }

        // فحص متغيرات البيئة المطلوبة
        const requiredEnvVars = [
            'DB_HOST',
            'DB_NAME',
            'DB_USER',
            'SESSION_SECRET'
        ];

        healthCheck.environment_variables = {};
        for (const envVar of requiredEnvVars) {
            healthCheck.environment_variables[envVar] = {
                status: process.env[envVar] ? 'OK' : 'MISSING',
                exists: !!process.env[envVar]
            };

            if (!process.env[envVar] && healthCheck.status === 'OK') {
                healthCheck.status = 'DEGRADED';
            }
        }

        // تحديد رمز الحالة HTTP
        let statusCode = 200;
        if (healthCheck.status === 'DEGRADED') {
            statusCode = 200; // لا يزال يعمل ولكن مع مشاكل
        } else if (healthCheck.status === 'ERROR') {
            statusCode = 503; // خدمة غير متاحة
        }

        res.status(statusCode).json(healthCheck);

    } catch (error) {
        console.error('Health check error:', error);
        res.status(503).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: error.message,
            uptime: process.uptime()
        });
    }
});

/**
 * فحص صحة قاعدة البيانات فقط
 * GET /health/database
 */
router.get('/database', async (req, res) => {
    try {
        await sequelize.authenticate();
        
        // فحص بعض الجداول الأساسية
        const tables = await sequelize.getQueryInterface().showAllTables();
        
        res.json({
            status: 'OK',
            timestamp: new Date().toISOString(),
            database: {
                connected: true,
                dialect: sequelize.getDialect(),
                host: sequelize.config.host,
                database: sequelize.config.database,
                tablesCount: tables.length,
                tables: tables.slice(0, 10) // أول 10 جداول فقط
            }
        });
    } catch (error) {
        res.status(503).json({
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            database: {
                connected: false,
                error: error.message
            }
        });
    }
});

/**
 * فحص صحة النظام المبسط
 * GET /health/simple
 */
router.get('/simple', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

/**
 * معلومات النظام
 * GET /health/info
 */
router.get('/info', (req, res) => {
    const info = {
        name: 'نظام إدارة المتاجر الذكي',
        version: process.env.npm_package_version || '1.0.0',
        description: 'نظام شامل لإدارة المتاجر والطلبات والعملاء',
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        platform: os.platform(),
        arch: os.arch(),
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
        features: [
            'إدارة المتاجر',
            'إدارة العملاء',
            'إدارة الطلبات',
            'نظام الإشعارات',
            'البحث والفلتر المتقدم',
            'واجهة مستخدم احترافية'
        ],
        endpoints: {
            admin: '/admin',
            customer: '/customers',
            api: '/api',
            health: '/health'
        }
    };

    res.json(info);
});

/**
 * إحصائيات الأداء
 * GET /health/metrics
 */
router.get('/metrics', async (req, res) => {
    try {
        const metrics = {
            timestamp: new Date().toISOString(),
            process: {
                pid: process.pid,
                uptime: process.uptime(),
                memoryUsage: process.memoryUsage(),
                cpuUsage: process.cpuUsage()
            },
            system: {
                platform: os.platform(),
                arch: os.arch(),
                totalMemory: os.totalmem(),
                freeMemory: os.freemem(),
                loadAverage: os.loadavg(),
                cpuCount: os.cpus().length,
                networkInterfaces: Object.keys(os.networkInterfaces())
            },
            environment: {
                nodeVersion: process.version,
                nodeEnv: process.env.NODE_ENV,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            }
        };

        // إضافة إحصائيات قاعدة البيانات إذا كانت متاحة
        try {
            await sequelize.authenticate();
            const [results] = await sequelize.query('SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ?', {
                replacements: [sequelize.config.database]
            });
            metrics.database = {
                connected: true,
                tablesCount: results[0].count
            };
        } catch (error) {
            metrics.database = {
                connected: false,
                error: error.message
            };
        }

        res.json(metrics);
    } catch (error) {
        res.status(500).json({
            error: 'Failed to collect metrics',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;
