/**
 * أنماط نظام الإشعارات المحترف
 * Professional Notification System Styles
 */

/* متغيرات الألوان */
:root {
    --notification-primary: #007bff;
    --notification-success: #28a745;
    --notification-warning: #ffc107;
    --notification-danger: #dc3545;
    --notification-info: #17a2b8;
    --notification-light: #f8f9fa;
    --notification-dark: #343a40;
    --notification-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --notification-border-radius: 8px;
}

/* أيقونة الجرس */
.notification-bell {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.notification-bell:hover {
    color: var(--notification-primary);
    transform: scale(1.1);
}

.notification-bell .fa-bell {
    color: #fff;
    transition: color 0.3s ease;
}

.notification-bell:hover .fa-bell {
    color: #22c55e;
}

/* عداد الإشعارات */
#notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--notification-danger);
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: none;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: var(--notification-shadow);
    animation: pulse 2s infinite;
    z-index: 10;
}

.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--notification-danger);
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: var(--notification-shadow);
    animation: pulse 2s infinite;
    z-index: 10;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* قائمة الإشعارات المنسدلة */
.notifications-dropdown {
    min-width: 350px;
    max-width: 400px;
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-radius: var(--notification-border-radius);
    max-height: 500px;
    overflow-y: auto;
}

.notifications-dropdown .dropdown-header {
    background: linear-gradient(135deg, var(--notification-primary), var(--notification-info));
    color: white;
    font-weight: 600;
    padding: 15px 20px;
    border-radius: var(--notification-border-radius) var(--notification-border-radius) 0 0;
    margin: 0;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.notifications-dropdown .dropdown-divider {
    margin: 0;
    border-color: #e9ecef;
}

/* عنصر الإشعار */
.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: flex-start;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-2px);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 4px solid var(--notification-primary);
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 8px;
    height: 8px;
    background: var(--notification-primary);
    border-radius: 50%;
    transform: translateY(-50%);
}

/* أيقونة الإشعار */
.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
    margin-right: 15px;
}

.notification-icon .fa-shopping-cart { background: var(--notification-success); }
.notification-icon .fa-truck { background: var(--notification-info); }
.notification-icon .fa-user { background: var(--notification-warning); }
.notification-icon .fa-cog { background: var(--notification-dark); }
.notification-icon .fa-exclamation-triangle { background: var(--notification-warning); }
.notification-icon .fa-check-circle { background: var(--notification-success); }
.notification-icon .fa-info-circle { background: var(--notification-info); }
.notification-icon .fa-times-circle { background: var(--notification-danger); }
.notification-icon .fa-bell { background: var(--notification-primary); }

/* محتوى الإشعار */
.notification-content {
    flex: 1;
    min-width: 0;
    line-height: 1.4;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--notification-dark);
    margin-bottom: 4px;
    line-height: 1.3;
}

.notification-message {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 4px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    font-size: 11px;
    color: #adb5bd;
    display: flex;
    align-items: center;
}

.notification-time i {
    margin-right: 4px;
}

/* أزرار الإجراءات */
.notifications-actions {
    padding: 10px 20px;
    background: #f8f9fa;
    border-radius: 0 0 var(--notification-border-radius) var(--notification-border-radius);
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.btn-mark-all-read {
    font-size: 12px;
    padding: 6px 16px;
    border-radius: 15px;
    background: var(--notification-primary);
    border: none;
    color: white;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-mark-all-read:hover {
    background: #0056b3;
    transform: translateY(-1px);
    color: white;
}

/* حالة فارغة */
.notifications-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.notifications-empty i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
    color: #adb5bd;
}

/* حالة التحميل */
.notifications-loading {
    text-align: center;
    padding: 20px;
    color: #6c757d;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--notification-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثيرات الحركة */
.notification-item {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.notification-bell.has-notifications {
    animation: bellRing 0.5s ease-in-out;
}

@keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(10deg); }
    75% { transform: rotate(-10deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .notifications-dropdown {
        min-width: 300px;
        max-width: 320px;
    }
    
    .notification-item {
        padding: 12px 15px;
    }
    
    .notification-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
        margin-right: 12px;
    }
    
    .notification-title {
        font-size: 13px;
    }
    
    .notification-message {
        font-size: 12px;
    }
}

/* حالات التركيز للوصولية */
.notification-item:focus {
    outline: 2px solid var(--notification-primary);
    outline-offset: -2px;
}

.notification-bell:focus {
    outline: 2px solid var(--notification-primary);
    outline-offset: 2px;
}
