/**
 * أنماط نظام الإشعارات المحترف
 * Professional Notification System Styles
 */

/* متغيرات الألوان المذهلة */
:root {
    --notification-primary: #667eea;
    --notification-primary-light: #7c3aed;
    --notification-success: #4facfe;
    --notification-success-light: #00f2fe;
    --notification-danger: #f093fb;
    --notification-danger-light: #f5576c;
    --notification-warning: #fa709a;
    --notification-warning-light: #fee140;
    --notification-info: #4299e1;
    --notification-light: #f7fafc;
    --notification-dark: #1a202c;
    --notification-white: #ffffff;

    /* التدرجات المذهلة */
    --notification-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --notification-gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --notification-gradient-danger: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --notification-gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    /* الظلال المذهلة */
    --notification-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --notification-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --notification-shadow-colored: 0 10px 25px rgba(102, 126, 234, 0.3);
    --notification-border-radius: 12px;
    --notification-border-radius-lg: 16px;
    --notification-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* أيقونة الجرس المذهلة */
.notification-bell {
    position: relative;
    cursor: pointer;
    transition: var(--notification-transition);
    padding: 0.75rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.notification-bell:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.15) rotate(15deg);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.notification-bell i {
    font-size: 1.25rem;
    color: var(--notification-white);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* عداد الإشعارات المذهل */
.notification-badge,
#notificationBadge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--notification-gradient-danger);
    color: white;
    border-radius: 50%;
    min-width: 22px;
    height: 22px;
    font-size: 0.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--notification-shadow);
    animation: pulseGlow 2s infinite;
}

@keyframes pulseGlow {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(240, 147, 251, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(240, 147, 251, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(240, 147, 251, 0);
    }
}

/* قائمة الإشعارات المنسدلة المذهلة */
.notifications-dropdown,
.dropdown-menu {
    min-width: 380px;
    max-width: 420px;
    border: none;
    box-shadow: var(--notification-shadow-colored);
    border-radius: var(--notification-border-radius-lg);
    max-height: 500px;
    overflow-y: auto;
    background: var(--notification-white);
    backdrop-filter: blur(20px);
    animation: dropdownSlide 0.3s ease-out;
}

@keyframes dropdownSlide {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.notifications-dropdown .dropdown-header,
.dropdown-header {
    background: var(--notification-gradient-primary);
    color: white;
    font-weight: 700;
    padding: 1.5rem 2rem;
    border-radius: var(--notification-border-radius-lg) var(--notification-border-radius-lg) 0 0;
    margin: 0;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
    overflow: hidden;
}

.dropdown-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: headerShimmer 3s ease-in-out infinite;
}

@keyframes headerShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.notifications-dropdown .dropdown-divider,
.dropdown-divider {
    margin: 0;
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.2), transparent);
}

/* عنصر الإشعار المذهل */
.notification-item,
.dropdown-item.notification-item {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    transition: var(--notification-transition);
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: flex-start;
    background: var(--notification-white);
    margin: 0;
}

.notification-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(5px);
    box-shadow: inset 4px 0 0 var(--notification-primary);
}

.notification-item:last-child {
    border-bottom: none;
    border-radius: 0 0 var(--notification-border-radius-lg) var(--notification-border-radius-lg);
}

.notification-item.unread {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
    border-left: 4px solid var(--notification-primary);
    position: relative;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 15px;
    width: 10px;
    height: 10px;
    background: var(--notification-gradient-primary);
    border-radius: 50%;
    transform: translateY(-50%);
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
    animation: unreadPulse 2s infinite;
}

@keyframes unreadPulse {
    0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
    50% { opacity: 0.7; transform: translateY(-50%) scale(1.1); }
}

/* أيقونة الإشعار المذهلة */
.notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: white;
    flex-shrink: 0;
    margin-right: 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--notification-shadow);
}

.notification-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.notification-item:hover .notification-icon::before {
    left: 100%;
}

.notification-icon .fa-shopping-cart { background: var(--notification-gradient-success); }
.notification-icon .fa-truck { background: var(--notification-gradient-primary); }
.notification-icon .fa-user { background: var(--notification-gradient-warning); }
.notification-icon .fa-cog { background: var(--notification-gradient-primary); }
.notification-icon .fa-exclamation-triangle { background: var(--notification-gradient-warning); }
.notification-icon .fa-check-circle { background: var(--notification-gradient-success); }
.notification-icon .fa-info-circle { background: var(--notification-gradient-primary); }
.notification-icon .fa-times-circle { background: var(--notification-gradient-danger); }
.notification-icon .fa-bell { background: var(--notification-gradient-primary); }

/* محتوى الإشعار المذهل */
.notification-content {
    flex: 1;
    min-width: 0;
    line-height: 1.5;
}

.notification-title {
    font-size: 1rem;
    font-weight: 700;
    color: var(--notification-dark);
    margin-bottom: 0.5rem;
    line-height: 1.3;
    background: linear-gradient(135deg, var(--notification-primary), var(--notification-primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.notification-message {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.notification-time {
    font-size: 0.75rem;
    color: #9ca3af;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.notification-time i {
    margin-right: 0.25rem;
    color: var(--notification-primary);
}

/* شارة الإشعار غير المقروء */
.notification-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    background: var(--notification-gradient-danger);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(240, 147, 251, 0.5);
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

/* أزرار الإجراءات المذهلة */
.notifications-actions {
    padding: 1.25rem 2rem;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 0 0 var(--notification-border-radius-lg) var(--notification-border-radius-lg);
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    text-align: center;
}

.btn-mark-all-read {
    font-size: 0.875rem;
    padding: 0.75rem 2rem;
    border-radius: var(--notification-border-radius);
    background: var(--notification-gradient-primary);
    border: none;
    color: white;
    transition: var(--notification-transition);
    text-decoration: none;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn-mark-all-read::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn-mark-all-read:hover::before {
    left: 100%;
}

.btn-mark-all-read:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    color: white;
}

/* حالة فارغة مذهلة */
.notifications-empty {
    text-align: center;
    padding: 3rem 2rem;
    color: #6b7280;
}

.notifications-empty i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.3;
    color: var(--notification-primary);
    background: var(--notification-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* حالة التحميل المذهلة */
.notifications-loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(102, 126, 234, 0.1);
    border-top: 3px solid var(--notification-primary);
    border-radius: 50%;
    animation: spinGlow 1s linear infinite;
    display: inline-block;
    margin-right: 0.75rem;
}

@keyframes spinGlow {
    0% {
        transform: rotate(0deg);
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.6);
    }
    100% {
        transform: rotate(360deg);
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
    }
}

/* أنماط التوست المذهلة */
.toast-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 9999;
}

.toast {
    background: var(--notification-white);
    border-radius: var(--notification-border-radius-lg);
    box-shadow: var(--notification-shadow-colored);
    border: none;
    margin-bottom: 1rem;
    overflow: hidden;
    backdrop-filter: blur(20px);
    animation: toastSlideIn 0.3s ease-out;
}

@keyframes toastSlideIn {
    0% {
        opacity: 0;
        transform: translateX(100%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.toast-header {
    background: var(--notification-gradient-primary);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 700;
}

.toast-body {
    padding: 1.5rem;
    color: var(--notification-dark);
    font-weight: 500;
}

/* تأثيرات الحركة المذهلة */
.notification-item {
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(30px) scale(0.9);
    }
    50% {
        opacity: 0.7;
        transform: translateX(-5px) scale(1.02);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.notification-bell.has-notifications {
    animation: bellRingGlow 0.6s ease-in-out;
}

@keyframes bellRingGlow {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        box-shadow: 0 0 0 rgba(102, 126, 234, 0);
    }
    25% {
        transform: rotate(15deg) scale(1.1);
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: rotate(-15deg) scale(1.1);
        box-shadow: 0 0 25px rgba(102, 126, 234, 0.6);
    }
    75% {
        transform: rotate(8deg) scale(1.05);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }
}

/* تأثيرات التمرير المذهلة */
.notifications-dropdown::-webkit-scrollbar {
    width: 6px;
}

.notifications-dropdown::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 3px;
}

.notifications-dropdown::-webkit-scrollbar-thumb {
    background: var(--notification-gradient-primary);
    border-radius: 3px;
}

.notifications-dropdown::-webkit-scrollbar-thumb:hover {
    background: var(--notification-gradient-danger);
}

/* تأثيرات إضافية */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.notification-glow {
    animation: notificationGlow 2s ease-in-out infinite;
}

@keyframes notificationGlow {
    0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.3); }
    50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.6); }
}

/* التصميم المتجاوب المذهل */
@media (max-width: 768px) {
    .notifications-dropdown {
        min-width: 320px;
        max-width: 380px;
        margin-left: -160px;
        border-radius: var(--notification-border-radius);
    }

    .notification-item {
        padding: 1rem 1.25rem;
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-right: 0.75rem;
    }

    .notification-title {
        font-size: 0.875rem;
    }

    .notification-message {
        font-size: 0.8125rem;
    }

    .notification-time {
        font-size: 0.6875rem;
    }

    .dropdown-header {
        padding: 1.25rem 1.5rem;
        font-size: 1rem;
    }

    .toast-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .toast {
        margin-bottom: 0.75rem;
    }
}

/* حالات التركيز للوصولية المذهلة */
.notification-item:focus {
    outline: 3px solid var(--notification-primary);
    outline-offset: -3px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    box-shadow: inset 0 0 0 2px var(--notification-primary);
}

.notification-bell:focus {
    outline: 3px solid var(--notification-primary);
    outline-offset: 3px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.btn-mark-all-read:focus {
    outline: 3px solid var(--notification-primary);
    outline-offset: 3px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* تأثيرات إضافية للتفاعل */
.notification-item:active {
    transform: scale(0.98);
}

.notification-bell:active {
    transform: scale(0.95);
}

/* تحسينات الأداء */
.notification-item,
.notification-bell,
.btn-mark-all-read {
    will-change: transform, box-shadow;
}

/* تأثيرات الظهور التدريجي */
.notifications-dropdown.show {
    animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}
