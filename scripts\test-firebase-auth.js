#!/usr/bin/env node

/**
 * اختبار مصادقة Firebase وإعدادات المشروع
 */

require('dotenv').config();
const admin = require('firebase-admin');
const { serviceAccountPath, firebaseConfig, vapidKey } = require('../config/firebase');

async function testFirebaseAuth() {
  console.log('🔐 اختبار مصادقة Firebase...\n');

  try {
    // 1. فحص Service Account
    console.log('📋 فحص Service Account...');
    const serviceAccount = require(`../${serviceAccountPath}`);
    
    const requiredFields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
    const missingFields = requiredFields.filter(field => !serviceAccount[field]);
    
    if (missingFields.length > 0) {
      console.error(`❌ حقول مفقودة في Service Account: ${missingFields.join(', ')}`);
      return false;
    }
    
    console.log('✅ Service Account صحيح');
    console.log(`   - Project ID: ${serviceAccount.project_id}`);
    console.log(`   - Client Email: ${serviceAccount.client_email}`);

    // 2. تهيئة Firebase Admin SDK
    console.log('\n⚙️ تهيئة Firebase Admin SDK...');
    
    // تحقق من عدم تهيئة Firebase مسبقاً
    if (admin.apps.length === 0) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
    }
    
    console.log('✅ تم تهيئة Firebase Admin SDK بنجاح');

    // 3. اختبار الاتصال بـ Firebase
    console.log('\n🌐 اختبار الاتصال بـ Firebase...');
    
    try {
      // محاولة إرسال رسالة تجريبية (سيفشل لعدم وجود توكن، لكن سيؤكد الاتصال)
      await admin.messaging().send({
        token: 'test-token-that-will-fail',
        notification: { title: 'Test', body: 'Test' }
      });
    } catch (testError) {
      if (testError.code === 'messaging/invalid-argument' || 
          testError.code === 'messaging/invalid-registration-token') {
        console.log('✅ الاتصال بـ Firebase يعمل (خطأ متوقع للتوكن التجريبي)');
      } else {
        console.error('❌ مشكلة في الاتصال بـ Firebase:', testError.message);
        return false;
      }
    }

    // 4. فحص إعدادات المشروع
    console.log('\n📊 فحص إعدادات المشروع...');
    
    if (serviceAccount.project_id !== firebaseConfig.projectId) {
      console.error('❌ عدم تطابق Project ID بين Service Account وإعدادات Firebase');
      console.log(`   Service Account: ${serviceAccount.project_id}`);
      console.log(`   Firebase Config: ${firebaseConfig.projectId}`);
      return false;
    }
    
    console.log('✅ إعدادات المشروع متطابقة');

    // 5. فحص مفتاح VAPID
    console.log('\n🔑 فحص مفتاح VAPID...');
    
    if (!vapidKey) {
      console.error('❌ مفتاح VAPID غير موجود');
      return false;
    }
    
    if (vapidKey.length < 80) {
      console.error('❌ مفتاح VAPID قصير جداً');
      return false;
    }
    
    console.log('✅ مفتاح VAPID صحيح');
    console.log(`   الطول: ${vapidKey.length} حرف`);

    // 6. فحص أذونات Firebase
    console.log('\n🔒 فحص أذونات Firebase...');
    
    try {
      // محاولة الوصول لإعدادات المشروع
      const app = admin.app();
      console.log('✅ أذونات Firebase صحيحة');
      console.log(`   اسم التطبيق: ${app.name}`);
    } catch (permError) {
      console.error('❌ مشكلة في أذونات Firebase:', permError.message);
      return false;
    }

    console.log('\n✅ جميع اختبارات المصادقة نجحت!');
    console.log('\n💡 الخطوات التالية:');
    console.log('   1. امسح cache المتصفح');
    console.log('   2. أعد تحميل الصفحة');
    console.log('   3. جرب تفعيل الإشعارات مرة أخرى');
    
    return true;

  } catch (error) {
    console.error('\n❌ فشل اختبار المصادقة:', error.message);
    
    if (error.code === 'auth/invalid-credential') {
      console.log('\n🔧 حلول مقترحة:');
      console.log('   1. تحقق من صحة ملف Service Account');
      console.log('   2. تأكد من تفعيل Firebase Admin SDK في Firebase Console');
      console.log('   3. تحقق من أذونات الملف');
    }
    
    return false;
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testFirebaseAuth()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('خطأ غير متوقع:', error);
      process.exit(1);
    });
}

module.exports = testFirebaseAuth;
