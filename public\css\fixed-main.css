/**
 * تنسيق احترافي مصحح - اللون الأساسي #B2CD9C
 */

/* ========== متغيرات الألوان ========== */
:root {
  /* الألوان الأساسية */
  --primary-color: #B2CD9C;
  --primary-dark: #8FB574;
  --primary-light: #C5D9B1;
  --primary-lighter: #E8F0E3;
  
  /* ألوان الحالة */
  --success-color: #22c55e;
  --success-light: #dcfce7;
  --warning-color: #f59e0b;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-light: #fee2e2;
  --info-color: #3b82f6;
  --info-light: #dbeafe;
  
  /* ألوان الخلفية */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-dark: #1e293b;
  
  /* ألوان النصوص */
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --text-white: #ffffff;
  
  /* الحدود والظلال */
  --border-color: #e2e8f0;
  --border-dark: #cbd5e1;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  
  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* نصف الأقطار */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
}

/* ========== إعدادات عامة ========== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* ========== العناوين ========== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* ========== الروابط ========== */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* ========== الأزرار ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* أنواع الأزرار */
.btn-primary {
  color: var(--text-white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--text-white);
  text-decoration: none;
}

.btn-secondary {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-dark);
  color: var(--text-primary);
  text-decoration: none;
}

.btn-success {
  color: var(--text-white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
  background-color: #16a34a;
  border-color: #16a34a;
  color: var(--text-white);
  text-decoration: none;
}

.btn-warning {
  color: var(--text-white);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
  border-color: #d97706;
  color: var(--text-white);
  text-decoration: none;
}

.btn-danger {
  color: var(--text-white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
  background-color: #dc2626;
  border-color: #dc2626;
  color: var(--text-white);
  text-decoration: none;
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: 0.75rem;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 1.125rem;
}

.btn-block {
  width: 100%;
}

/* أزرار الخطوط الخارجية */
.btn-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
  color: var(--text-white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  text-decoration: none;
}

/* ========== الكروت ========== */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--spacing-lg);
  background-color: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-color);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background-color: var(--bg-tertiary);
  border-top: 1px solid var(--border-color);
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.card-text {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

/* ========== النماذج ========== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-weight: 500;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-lighter);
}

.form-control:disabled {
  background-color: var(--bg-tertiary);
  opacity: 0.6;
  cursor: not-allowed;
}

.form-text {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* ========== التنبيهات ========== */
.alert {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.alert-success {
  color: #065f46;
  background-color: var(--success-light);
  border-color: #a7f3d0;
}

.alert-warning {
  color: #92400e;
  background-color: var(--warning-light);
  border-color: #fde68a;
}

.alert-danger {
  color: #991b1b;
  background-color: var(--danger-light);
  border-color: #fecaca;
}

.alert-info {
  color: #155e75;
  background-color: var(--info-light);
  border-color: #a5f3fc;
}

/* ========== الجداول ========== */
.table {
  width: 100%;
  margin-bottom: var(--spacing-lg);
  background-color: var(--bg-primary);
  border-collapse: collapse;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: right;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.table th {
  background-color: var(--bg-tertiary);
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-dark);
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
}

/* ========== الشبكة ========== */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-lg);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
  flex: 1;
  padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; padding: 0 var(--spacing-sm); }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; padding: 0 var(--spacing-sm); }
.col-3 { flex: 0 0 25%; max-width: 25%; padding: 0 var(--spacing-sm); }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; padding: 0 var(--spacing-sm); }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; padding: 0 var(--spacing-sm); }
.col-6 { flex: 0 0 50%; max-width: 50%; padding: 0 var(--spacing-sm); }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; padding: 0 var(--spacing-sm); }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; padding: 0 var(--spacing-sm); }
.col-9 { flex: 0 0 75%; max-width: 75%; padding: 0 var(--spacing-sm); }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; padding: 0 var(--spacing-sm); }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; padding: 0 var(--spacing-sm); }
.col-12 { flex: 0 0 100%; max-width: 100%; padding: 0 var(--spacing-sm); }

/* ========== المسافات ========== */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* ========== الأدوات المساعدة ========== */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-white { color: var(--text-white) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-white { background-color: var(--bg-primary) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ========== الاستجابة للشاشات ========== */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .btn {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.875rem;
  }

  .card-body {
    padding: var(--spacing-md);
  }

  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}
