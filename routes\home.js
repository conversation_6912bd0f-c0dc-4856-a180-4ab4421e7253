const express = require('express');
const router = express.Router();
const DashboardController = require('../controllers/DashboardController');

// عرض الصفحة الرئيسية
router.get('/', DashboardController.index.bind(DashboardController));

// صفحة عرض النظام الجديد
router.get('/demo', (req, res) => {
    res.render('demo', {
        title: 'عرض النظام الجديد - نظام إدارة المتاجر الذكي',
        layout: false // استخدام تخطيط مخصص
    });
});

module.exports = router;
