#!/usr/bin/env node

/**
 * اختبار سريع لإشعارات Firebase
 * يرسل إشعار تجريبي لجميع المديرين المسجلين
 */

require('dotenv').config();
const admin = require('firebase-admin');
const { AdminToken } = require('../models');
const { serviceAccountPath } = require('../config/firebase');
const FirebaseMessagingService = require('../services/FirebaseMessagingService');

// تهيئة Firebase Admin SDK
try {
  const serviceAccount = require(`../${serviceAccountPath}`);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
  console.log('✅ Firebase Admin SDK initialized');
} catch (error) {
  console.error('❌ Failed to initialize Firebase Admin SDK:', error);
  process.exit(1);
}

async function testFirebaseNotifications() {
  console.log('\n🧪 بدء اختبار إشعارات Firebase...\n');

  try {
    // 1. فحص التوكنات المسجلة
    console.log('📱 فحص التوكنات المسجلة...');
    const adminTokens = await AdminToken.findAll();
    
    if (adminTokens.length === 0) {
      console.error('❌ لا توجد توكنات مديرين مسجلة');
      console.log('💡 تأكد من تسجيل الدخول كمدير وتفعيل الإشعارات أولاً');
      return;
    }
    
    console.log(`✅ تم العثور على ${adminTokens.length} توكن مسجل`);

    // 2. إعداد الإشعار التجريبي
    const testNotification = {
      title: '🧪 اختبار الإشعارات',
      body: `تم إرسال هذا الإشعار في ${new Date().toLocaleString('ar-EG')}`
    };

    const testData = {
      type: 'test',
      timestamp: Date.now().toString(),
      clickAction: '/admin/dashboard'
    };

    console.log('\n📤 إرسال الإشعار التجريبي...');
    console.log(`📋 العنوان: ${testNotification.title}`);
    console.log(`📝 المحتوى: ${testNotification.body}`);

    // 3. إرسال الإشعار
    const result = await FirebaseMessagingService.sendToAllAdmins(testNotification, testData);

    // 4. عرض النتائج
    console.log('\n📊 نتائج الاختبار:');
    console.log(`✅ نجح: ${result.successCount}`);
    console.log(`❌ فشل: ${result.failureCount}`);
    console.log(`📈 معدل النجاح: ${((result.successCount / adminTokens.length) * 100).toFixed(1)}%`);

    if (result.failureCount > 0) {
      console.log('\n⚠️ تفاصيل الأخطاء:');
      result.results.forEach((res, index) => {
        if (!res.success) {
          console.log(`   ${index + 1}. ${res.error}`);
        }
      });
    }

    // 5. توصيات
    console.log('\n💡 التوصيات:');
    if (result.successCount === 0) {
      console.log('   - تحقق من إعدادات Firebase في config/firebase.js');
      console.log('   - تأكد من صحة Service Account');
      console.log('   - فحص اتصال الإنترنت');
      console.log('   - تشغيل: node scripts/firebase-diagnostics.js full');
    } else if (result.failureCount > 0) {
      console.log('   - تنظيف التوكنات غير الصالحة: node scripts/firebase-diagnostics.js cleanup');
      console.log('   - فحص التوكنات: node scripts/firebase-diagnostics.js tokens');
    } else {
      console.log('   ✅ النظام يعمل بشكل مثالي!');
    }

  } catch (error) {
    console.error('\n❌ فشل الاختبار:', error.message);
    console.log('\n🔧 خطوات استكشاف الأخطاء:');
    console.log('   1. تحقق من إعدادات Firebase');
    console.log('   2. تأكد من صحة Service Account');
    console.log('   3. فحص اتصال الإنترنت');
    console.log('   4. مراجعة سجلات الأخطاء');
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testFirebaseNotifications()
    .then(() => {
      console.log('\n✅ انتهى الاختبار');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ خطأ في الاختبار:', error);
      process.exit(1);
    });
}

module.exports = testFirebaseNotifications;
