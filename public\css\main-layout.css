/**
 * أنماط التخطيط الرئيسي المذهل
 * Amazing Professional Main Layout Styles
 */

/* متغيرات الألوان المذهلة */
:root {
  /* الألوان الأساسية - تدرج أزرق وأخضر فاتح */
  --primary-color: #0ea5e9;     /* sky-500 */
  --primary-dark: #0284c7;      /* sky-600 */
  --primary-light: #38bdf8;     /* sky-400 */

  /* الثانوي - أخضر فاتح */
  --secondary-color: #10b981;   /* emerald-500 */
  --secondary-dark: #059669;    /* emerald-600 */
  --secondary-light: #34d399;   /* emerald-400 */

  /* ألوان المساعدة */
  --accent-color: #f97316;      /* orange-500 */
  --accent-dark: #ea580c;       /* orange-600 */
  --info-color: #3b82f6;        /* blue-500 */
  --success-color: #22c55e;     /* green-500 */
  --warning-color: #facc15;     /* yellow-400 */
  --danger-color: #ef4444;      /* red-500 */

  /* الرمادي الفاتح */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;

  /* تدرجات فاتحة */
  --gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #10b981 100%);
  --gradient-secondary: linear-gradient(135deg, #f97316 0%, #facc15 100%);
  --gradient-success: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --gradient-warning: linear-gradient(135deg, #facc15 0%, #f97316 100%);
  --gradient-danger: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);
  --gradient-info: linear-gradient(135deg, #3b82f6 0%, #0ea5e9 100%);

  /* الظلال */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);

  /* الحدود والانتقالات */
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 20px;
  --transition: all 0.3s ease;
  --transition-fast: all 0.15s ease;
}

/* إعدادات عامة مذهلة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-bg);
    background-attachment: fixed;
    color: var(--gray-800);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* خلفية متحركة مذهلة */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 58, 237, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(240, 147, 251, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundFloat 20s ease-in-out infinite;
}

@keyframes backgroundFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* شريط التنقل المذهل */
.navbar-custom {
    background: var(--gradient-primary);
    backdrop-filter: blur(20px);
    border: none;
    box-shadow: var(--shadow-colored);
    position: relative;
    z-index: 1000;
    padding: 1rem 0;
}

.navbar-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(255,255,255,0.1) 0%,
        rgba(255,255,255,0.05) 50%,
        rgba(255,255,255,0.1) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.navbar-brand {
    font-weight: 700;
    color: var(--white) !important;
    font-size: 1.75rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 2;
}

.nav-link {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
    transition: var(--transition);
    padding: 0.75rem 1.25rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.nav-link:hover {
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.nav-link.active {
    background: var(--accent-color);
    color: var(--white) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* أنماط المحتوى الرئيسي */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.container-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.container-fluid {
    max-width: 1400px;
}

/* أنماط البطاقات المذهلة */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    overflow: hidden;
    background: var(--white);
    position: relative;
    backdrop-filter: blur(10px);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    z-index: 1;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.card-header {
    background: var(--gradient-primary);
    color: var(--white);
    font-weight: 700;
    border: none;
    padding: 1.5rem 2rem;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.card:hover .card-header::before {
    left: 100%;
}

.card-body {
    padding: 2rem;
    position: relative;
}

.welcome-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-primary);
}

/* شبكة الإحصائيات المذهلة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 500;
}

/* شبكة الإجراءات */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.action-card h5 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

/* أنماط الأزرار المذهلة */
.btn {
    border-radius: var(--border-radius-lg);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    border: none;
    text-transform: none;
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
    font-size: 0.875rem;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-colored);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    color: var(--white);
}

.btn-success {
    background: var(--gradient-success);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
    color: var(--white);
}

.btn-danger {
    background: var(--gradient-secondary);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(245, 87, 108, 0.4);
    color: var(--white);
}

.btn-warning {
    background: var(--gradient-warning);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
}

.btn-warning:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(250, 112, 154, 0.4);
    color: var(--white);
}

.btn-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: var(--white);
    padding: 0.5rem 1.25rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 0.5rem;
    width: 100%;
    font-size: 0.875rem;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: var(--white);
}

/* أنماط النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* أنماط الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(178, 205, 156, 0.1);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: #f1f3f4;
}

/* أنماط القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
    padding: 0;
}

.dropdown-item:hover {
    background: var(--bg-light);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .container-main {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}

/* فئات الأدوات */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

/* تأثيرات الحركة */
.animate-fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-slide-in {
    animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* حالات التركيز للوصولية */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
