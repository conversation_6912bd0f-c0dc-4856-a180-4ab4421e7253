/**
 * أنماط التخطيط الرئيسي
 * Main Layout Styles
 */

/* متغيرات الألوان الأساسية */
:root {
    --primary-color: #B2CD9C;
    --secondary-color: #8FBC8F;
    --accent-color: #7BA05B;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --bg-light: #f8f9fa;
    --white: #ffffff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
    color: var(--text-dark);
    line-height: 1.6;
    min-height: 100vh;
}

/* أنماط الشريط العلوي */
.navbar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: var(--shadow);
    padding: 1rem 0;
    border-bottom: 3px solid var(--accent-color);
}

.navbar-brand {
    color: var(--white) !important;
    font-weight: 800;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem !important;
    border-radius: 25px;
    margin: 0 0.2rem;
}

.nav-link:hover {
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    background: var(--accent-color);
    color: var(--white) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* أنماط المحتوى الرئيسي */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.container-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.container-fluid {
    max-width: 1400px;
}

/* أنماط البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    background: var(--white);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    font-weight: 600;
    border: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

.welcome-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    border-left: 5px solid var(--primary-color);
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 500;
}

/* شبكة الإجراءات */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--white);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.action-card h5 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

/* أنماط الأزرار */
.btn {
    border-radius: 25px;
    font-weight: 500;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    box-shadow: 0 4px 15px rgba(178, 205, 156, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(178, 205, 156, 0.6);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
}

.btn-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin-bottom: 0.5rem;
    width: 100%;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: var(--white);
}

/* أنماط النماذج */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* أنماط الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(178, 205, 156, 0.1);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: #f1f3f4;
}

/* أنماط القوائم المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-lg);
    border-radius: 12px;
    padding: 0;
}

.dropdown-item:hover {
    background: var(--bg-light);
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .container-main {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}

/* فئات الأدوات */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-custom {
    box-shadow: var(--shadow-lg);
}

/* تأثيرات الحركة */
.animate-fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animate-slide-in {
    animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* حالات التركيز للوصولية */
.btn:focus,
.form-control:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
