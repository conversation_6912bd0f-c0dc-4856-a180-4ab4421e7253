// إنشاء إشعار جديد
await NotificationService.createNotification({
    title: 'عنوان الإشعار',
    message: 'نص الإشعار',
    type: 'info', // info, success, warning, error, order, promotion, system
    priority: 'normal', // low, normal, high, urgent
    customerId: null, // معرف العميل (اختياري)
    adminId: null, // معرف المدير (اختياري)
    actionUrl: null, // رابط الإجراء (اختياري)
    actionText: null, // نص زر الإجراء (اختياري)
    expiresAt: null // تاريخ انتهاء الصلاحية (اختياري)
});

// إرسال إشعار لجميع المستخدمين
await NotificationService.notifyAll({
    title: 'إشعار عام',
    message: 'رسالة لجميع المستخدمين'
});

// إرسال إشعار لعميل محدد
await NotificationService.notifyCustomer(customerId, {
    title: 'إشعار خاص',
    message: 'رسالة للعميل'
});

await NotificationService.notifyPromotion({
    title: 'عرض خاص',
    message: 'تفاصيل العرض',
    targetType: 'all', // all, customers
    priority: 'normal',
    expiresAt: new Date('2024-12-31')
});
