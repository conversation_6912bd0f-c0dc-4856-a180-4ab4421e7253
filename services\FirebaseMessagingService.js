const admin = require('firebase-admin');
const logger = require('../utils/logger');
const { AdminToken } = require('../models');
const { 
  notificationSettings, 
  invalidTokenErrorCodes, 
  retrySettings 
} = require('../config/firebase');

class FirebaseMessagingService {
  /**
   * إرسال إشعار لتوكن واحد
   */
  static async sendToToken(token, notification, data = {}) {
    try {
      const message = this.buildMessage(notification, data, token);
      const response = await admin.messaging().send(message);
      
      logger.info(`Notification sent successfully to token: ${token.substring(0, 20)}...`);
      return { success: true, response, token };
      
    } catch (error) {
      logger.error(`Failed to send notification to token ${token.substring(0, 20)}...:`, error.message);
      
      // التحقق من صحة التوكن
      if (invalidTokenErrorCodes.includes(error.code)) {
        await this.handleInvalidToken(token);
      }
      
      throw error;
    }
  }

  /**
   * إرسال إشعار لعدة توكنات
   */
  static async sendToMultipleTokens(tokens, notification, data = {}) {
    if (!tokens || tokens.length === 0) {
      logger.warn('No tokens provided for notification');
      return { successCount: 0, failureCount: 0, results: [] };
    }

    const promises = tokens.map(token => 
      this.sendToTokenWithRetry(token, notification, data)
    );

    const results = await Promise.allSettled(promises);
    
    const successCount = results.filter(r => 
      r.status === 'fulfilled' && r.value.success
    ).length;
    
    const failureCount = results.length - successCount;
    
    logger.info(`Notification batch results: ${successCount} successful, ${failureCount} failed`);
    
    return {
      successCount,
      failureCount,
      results: results.map(r => r.status === 'fulfilled' ? r.value : { success: false, error: r.reason })
    };
  }

  /**
   * إرسال إشعار لجميع المديرين
   */
  static async sendToAllAdmins(notification, data = {}) {
    try {
      const adminTokens = await AdminToken.findAll();
      
      if (adminTokens.length === 0) {
        logger.warn('No admin tokens found for notification');
        return { successCount: 0, failureCount: 0, results: [] };
      }

      const tokens = adminTokens.map(adminToken => adminToken.token);
      return await this.sendToMultipleTokens(tokens, notification, data);
      
    } catch (error) {
      logger.error('Error sending notification to all admins:', error);
      throw error;
    }
  }

  /**
   * إرسال إشعار مع إعادة المحاولة
   */
  static async sendToTokenWithRetry(token, notification, data = {}, retryCount = 0) {
    try {
      return await this.sendToToken(token, notification, data);
    } catch (error) {
      if (retryCount < retrySettings.maxRetries && !invalidTokenErrorCodes.includes(error.code)) {
        const delay = retrySettings.retryDelay * Math.pow(retrySettings.backoffMultiplier, retryCount);
        
        logger.info(`Retrying notification after ${delay}ms (attempt ${retryCount + 1}/${retrySettings.maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return await this.sendToTokenWithRetry(token, notification, data, retryCount + 1);
      }
      
      return { success: false, error: error.message, token };
    }
  }

  /**
   * بناء رسالة الإشعار
   */
  static buildMessage(notification, data = {}, token) {
    const message = {
      notification: {
        title: notification.title,
        body: notification.body
      },
      data: {
        ...data,
        timestamp: Date.now().toString()
      },
      token,
      android: notificationSettings.android,
      apns: notificationSettings.apns,
      webpush: {
        ...notificationSettings.webpush,
        notification: {
          ...notificationSettings.webpush.notification,
          ...notificationSettings.defaultOptions
        }
      }
    };

    return message;
  }

  /**
   * معالجة التوكنات غير الصالحة
   */
  static async handleInvalidToken(token) {
    try {
      const adminToken = await AdminToken.findOne({ where: { token } });
      
      if (adminToken) {
        await adminToken.destroy();
        logger.info(`Deleted invalid admin token: ${token.substring(0, 20)}...`);
      }
    } catch (error) {
      logger.error('Error deleting invalid token:', error);
    }
  }

  /**
   * التحقق من صحة التوكن
   */
  static async validateToken(token) {
    try {
      const message = {
        data: { test: 'true' },
        token,
        dryRun: true // لا يرسل الإشعار فعلياً
      };
      
      await admin.messaging().send(message);
      return true;
    } catch (error) {
      if (invalidTokenErrorCodes.includes(error.code)) {
        await this.handleInvalidToken(token);
        return false;
      }
      
      // خطأ آخر غير متعلق بصحة التوكن
      logger.warn(`Token validation failed with non-token error: ${error.message}`);
      return true; // نعتبر التوكن صالح في هذه الحالة
    }
  }

  /**
   * تنظيف التوكنات غير الصالحة
   */
  static async cleanupInvalidTokens() {
    try {
      const adminTokens = await AdminToken.findAll();
      const validationPromises = adminTokens.map(async (adminToken) => {
        const isValid = await this.validateToken(adminToken.token);
        return { token: adminToken, isValid };
      });

      const results = await Promise.allSettled(validationPromises);
      const invalidCount = results.filter(r => 
        r.status === 'fulfilled' && !r.value.isValid
      ).length;

      logger.info(`Token cleanup completed. Removed ${invalidCount} invalid tokens.`);
      return invalidCount;
      
    } catch (error) {
      logger.error('Error during token cleanup:', error);
      throw error;
    }
  }
}

module.exports = FirebaseMessagingService;
