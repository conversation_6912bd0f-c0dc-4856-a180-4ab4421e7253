<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-center">Login</h3>
                </div>
                <div class="card-body">
                    <% if (error && typeof error === 'string' && error.trim().length > 0) { %>
                        <div class="alert alert-danger"><%= error %></div>
                      <% } %>
                      
                      <% if (success && typeof success === 'string' && success.trim().length > 0) { %>
                        <div class="alert alert-success"><%= success %></div>
                      <% } %>
                    <form action="/customers/login" method="POST">
                        <div class="mb-3">
                            <label for="barcode" class="form-label">Barcode</label>
                            <input type="text" class="form-control" id="barcode" name="barcode" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Login</button>
                        </div>
                    </form>
                    <div class="text-center mt-3">
                        <p>Don't have an account? <a href="/customers/register">Register here</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 