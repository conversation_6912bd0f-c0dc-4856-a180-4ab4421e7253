/**
 * نظام إدارة المتاجر الذكي - JavaScript محدث
 * تحسينات التفاعل والتجربة المستخدم مع النظام الجديد
 */

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initializeSystem();

    // تهيئة التأثيرات
    initializeAnimations();

    // تهيئة النماذج
    initializeForms();

    // تهيئة الجداول
    initializeTables();

    // تهيئة الإشعارات
    initializeNotifications();

});

/**
 * تهيئة النظام العام
 */
function initializeSystem() {
    // إخفاء شاشة التحميل
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 300);
        }, 1000);
    }

    // تهيئة الشريط الجانبي للجوال
    initializeMobileSidebar();

    // تهيئة القوائم المنسدلة
    initializeDropdowns();

    // تهيئة التنبيهات التلقائية
    initializeAutoAlerts();
}

/**
 * تهيئة الشريط الجانبي للجوال
 */
function initializeMobileSidebar() {
    const sidebarToggle = document.querySelector('.navbar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            if (overlay) {
                overlay.classList.toggle('show');
            }
        });

        if (overlay) {
            overlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
            });
        }
    }
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.navbar-dropdown');

    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.navbar-dropdown-menu');

        dropdown.addEventListener('mouseenter', function() {
            if (menu) menu.classList.add('show');
        });

        dropdown.addEventListener('mouseleave', function() {
            if (menu) menu.classList.remove('show');
        });
    });
}

/**
 * تهيئة التنبيهات التلقائية
 */
function initializeAutoAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-dismissible')) {
                try {
                    const closeBtn = alert.querySelector('.btn-close');
                    if (closeBtn) {
                        closeBtn.click();
                    } else {
                        alert.style.display = 'none';
                    }
                } catch (e) {
                    alert.style.display = 'none';
                }
            }
        });
    }, 5000);

    // تأكيد الحذف
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('delete-btn') || e.target.closest('.delete-btn')) {
            if (!confirm('هل أنت متأكد من أنك تريد حذف هذا العنصر؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
}

/**
 * تهيئة التأثيرات والحركات
 */
function initializeAnimations() {
    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // مراقبة الكروت والعناصر
    const animatedElements = document.querySelectorAll('.card, .stats-card, .alert');
    animatedElements.forEach(el => {
        observer.observe(el);
    });

    // تأثير النقر على الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // تأثير الضغط
            this.classList.add('press-effect');
            setTimeout(() => {
                this.classList.remove('press-effect');
            }, 150);
        });
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // تحسين تجربة النماذج
    const formControls = document.querySelectorAll('.form-control');

    formControls.forEach(control => {
        // تأثير التركيز
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        control.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            validateField(this);
        });

        // التحقق أثناء الكتابة
        control.addEventListener('input', function() {
            clearTimeout(this.validateTimeout);
            this.validateTimeout = setTimeout(() => {
                validateField(this);
            }, 500);
        });
    });

    // تحسين النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> جاري المعالجة...';

                // إعادة تفعيل الزر بعد 5 ثوان في حالة عدم انتقال الصفحة
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }, 5000);
            }
        });
    });
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');

    // إزالة الفئات السابقة
    field.classList.remove('is-valid', 'is-invalid');

    // التحقق من الحقول المطلوبة
    if (required && !value) {
        field.classList.add('is-invalid');
        return false;
    }

    // التحقق من البريد الإلكتروني
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            field.classList.add('is-invalid');
            return false;
        }
    }

    // إذا وصلنا هنا، فالحقل صحيح
    if (value) {
        field.classList.add('is-valid');
    }

    return true;
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    const tables = document.querySelectorAll('.table');

    tables.forEach(table => {
        // إضافة إمكانية الترتيب
        const sortableHeaders = table.querySelectorAll('th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                sortTable(table, this);
            });
        });
    });
}

/**
 * ترتيب الجدول
 */
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentElement.children).indexOf(header);
    const isAscending = !header.classList.contains('asc');

    // إزالة فئات الترتيب من جميع الرؤوس
    table.querySelectorAll('th.sortable').forEach(th => {
        th.classList.remove('asc', 'desc');
    });

    // إضافة فئة الترتيب للرأس الحالي
    header.classList.add(isAscending ? 'asc' : 'desc');

    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();

        // محاولة التحويل إلى رقم
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ?
                aValue.localeCompare(bValue, 'ar') :
                bValue.localeCompare(aValue, 'ar');
        }
    });

    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * تهيئة نظام الإشعارات
 */
function initializeNotifications() {
    // إنشاء حاوي الإشعارات
    if (!document.getElementById('notifications-container')) {
        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = 'position-fixed';
        // تحديد الموضع بناءً على اتجاه النص
        const isRTL = document.dir === 'rtl' || document.documentElement.dir === 'rtl';
        container.style.cssText = `
            top: 20px;
            ${isRTL ? 'left: 20px;' : 'right: 20px;'}
            z-index: var(--z-modal);
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }
}

/**
 * إظهار إشعار محسن
 */
function showNotification(message, type = 'info', duration = 5000, options = {}) {
    const container = document.getElementById('notifications-container');
    if (!container) {
        // إنشاء الحاوي إذا لم يكن موجوداً
        initializeNotifications();
        return showNotification(message, type, duration, options);
    }

    const notification = document.createElement('div');
    const notificationId = 'notification-' + Date.now() + Math.random().toString(36).substr(2, 9);

    notification.id = notificationId;
    notification.className = `alert alert-${type} alert-dismissible mb-3 notification-item`;
    notification.style.cssText = 'pointer-events: auto; transform: translateX(100%); opacity: 0; transition: all 0.3s ease;';

    // إضافة الأيقونة والمحتوى
    const icon = getNotificationIcon(type);
    const title = options.title || getNotificationTitle(type);

    notification.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${icon}"></i>
        </div>
        <div class="alert-content">
            ${title ? `<div class="alert-title">${title}</div>` : ''}
            <div class="alert-message">${message}</div>
        </div>
        <button type="button" class="btn-close" onclick="removeNotification('${notificationId}')" aria-label="إغلاق">
            <i class="fas fa-times"></i>
        </button>
    `;

    container.appendChild(notification);

    // تأثير الظهور
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
        notification.style.opacity = '1';
    }, 10);

    // إزالة الإشعار تلقائياً
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notificationId);
        }, duration);
    }

    // إضافة تأثير صوتي (اختياري)
    if (options.sound !== false) {
        playNotificationSound(type);
    }

    return notificationId;
}

/**
 * الحصول على أيقونة الإشعار
 */
function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'warning': 'exclamation-triangle',
        'danger': 'times-circle',
        'error': 'times-circle',
        'info': 'info-circle',
        'primary': 'bell',
        'order': 'shopping-cart',
        'payment': 'credit-card',
        'delivery': 'truck',
        'system': 'cog',
        'promotion': 'tag'
    };
    return icons[type] || 'bell';
}

/**
 * الحصول على عنوان الإشعار
 */
function getNotificationTitle(type) {
    const titles = {
        'success': 'نجح!',
        'warning': 'تحذير!',
        'danger': 'خطأ!',
        'error': 'خطأ!',
        'info': 'معلومة',
        'primary': 'إشعار',
        'order': 'طلب جديد',
        'payment': 'دفع',
        'delivery': 'توصيل',
        'system': 'النظام',
        'promotion': 'عرض خاص'
    };
    return titles[type] || 'إشعار';
}

/**
 * إزالة إشعار محدد
 */
function removeNotification(notificationId) {
    const notification = document.getElementById(notificationId);
    if (notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }
}

/**
 * تشغيل صوت الإشعار (اختياري)
 */
function playNotificationSound(type) {
    // يمكن إضافة أصوات مختلفة لكل نوع إشعار
    try {
        const audio = new Audio();
        switch(type) {
            case 'success':
                // audio.src = '/sounds/success.mp3';
                break;
            case 'warning':
            case 'danger':
            case 'error':
                // audio.src = '/sounds/error.mp3';
                break;
            default:
                // audio.src = '/sounds/notification.mp3';
                break;
        }
        // audio.play().catch(() => {}); // تجاهل الأخطاء إذا لم يُسمح بالصوت
    } catch (e) {
        // تجاهل أخطاء الصوت
    }
}

/**
 * إزالة جميع الإشعارات
 */
function clearAllNotifications() {
    const container = document.getElementById('notifications-container');
    if (container) {
        const notifications = container.querySelectorAll('.notification-item');
        notifications.forEach(notification => {
            removeNotification(notification.id);
        });
    }
}

/**
 * عرض إشعار سريع
 */
function showQuickNotification(message, type = 'info') {
    return showNotification(message, type, 3000, { sound: false });
}

/**
 * عرض إشعار دائم (لا يختفي تلقائياً)
 */
function showPersistentNotification(message, type = 'info', options = {}) {
    return showNotification(message, type, 0, options);
}

// وظائف مساعدة عامة
window.SmartStore = {
    // نظام الإشعارات
    showNotification: showNotification,
    showQuickNotification: showQuickNotification,
    showPersistentNotification: showPersistentNotification,
    removeNotification: removeNotification,
    clearAllNotifications: clearAllNotifications,

    // عرض رسالة تأكيد
    confirm: function(message, callback) {
        if (confirm(message || 'هل أنت متأكد؟')) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },

    // إعادة تحميل الصفحة
    reload: function() {
        window.location.reload();
    },

    // الانتقال لصفحة أخرى
    redirect: function(url) {
        window.location.href = url;
    },

    // وظائف مساعدة إضافية
    formatNumber: function(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    },

    formatDate: function(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },

    formatTime: function(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }
};

