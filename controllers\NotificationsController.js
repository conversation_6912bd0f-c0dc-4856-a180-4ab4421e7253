const BaseController = require('./BaseController');
const { Notification, Customer, Admin } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const NotificationsFunction = require('../controllers/NotificationsFunction');


class NotificationsController extends BaseController {
    constructor() {
        super(Notification, 'notifications');
    }

    // عرض كل الإشعارات مع فلترة وترقيم الصفحات
    async index(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;
            const type = req.query.type;
            const priority = req.query.priority;
            const status = req.query.status;
            const search = req.query.search;

            // بناء شروط البحث
            const whereClause = {};

            if (type) whereClause.type = type;
            if (priority) whereClause.priority = priority;
            if (status === 'read') whereClause.readAt = { [Op.not]: null };
            if (status === 'unread') whereClause.readAt = null;
            if (search) {
                whereClause[Op.or] = [
                    { title: { [Op.like]: `%${search}%` } },
                    { message: { [Op.like]: `%${search}%` } }
                ];
            }

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Customer, as: 'customer', required: false },
                    { model: Admin, as: 'admin', required: false }
                ],
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            res.render('notifications/index', {
                notifications,
                currentPage: page,
                totalPages,
                totalCount: count,
                filters: { type, priority, status, search },
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1,
                nextPage: page + 1,
                prevPage: page - 1
            });
        } catch (error) {
            console.error(error);
            logger.error('Error fetching notifications:', error);
            res.status(500).render('error', { error });
        }
    }

    // صفحة إنشاء إشعار جديد
    async create(req, res) {
        try {
            const [customers, admins] = await Promise.all([
                Customer.findAll({ attributes: ['id', 'name', 'phoneNumber'] }),
                Admin.findAll({ attributes: ['id', 'name', 'userName'] })
            ]);

            res.render('notifications/create', {
                customers,
                admins,
                notification: {} // للنموذج الفارغ
            });
        } catch (error) {
            logger.error('Error loading create notification page:', error);
            res.status(500).render('error', { error });
        }
    }

    // حفظ الإشعار الجديد
    async insert(req, res) {
        try {
            const {
                title,
                message,
                type,
                priority,
                customerId,
                adminId,
                actionUrl,
                actionText,
                expiresAt,
                sendToAll
            } = req.body;

            // التحقق من صحة البيانات
            if (!title || !message) {
                req.flash('error', 'العنوان والرسالة مطلوبان');
                return res.redirect('/notifications/create');
            }

            // إذا كان الإرسال لجميع المستخدمين
            if (sendToAll) {
                await this.sendToAllUsers(req.body);
                req.flash('success', 'تم إرسال الإشعار لجميع المستخدمين بنجاح');
            } else {
                // إنشاء إشعار واحد
                await Notification.createNotification({
                    title,
                    message,
                    type: type || 'info',
                    priority: priority || 'normal',
                    customerId: customerId || null,
                    adminId: adminId || null,
                    actionUrl: actionUrl || null,
                    actionText: actionText || null,
                    expiresAt: expiresAt || null
                });
                req.flash('success', 'تم إنشاء الإشعار بنجاح');
            }

            res.redirect('/notifications');
        } catch (error) {
            logger.error('Error creating notification:', error);
            req.flash('error', 'حدث خطأ أثناء إنشاء الإشعار');
            res.redirect('/notifications/create');
        }
    }

    // إرسال إشعار لجميع المستخدمين
    async sendToAllUsers(data) {
        const { title, message, type, priority, actionUrl, actionText, expiresAt } = data;

        // جلب جميع العملاء والمتاجر
        const [customers] = await Promise.all([
            Customer.findAll({ attributes: ['id'] }),
        ]);

        const notifications = [];

        // إنشاء إشعارات للعملاء
        customers.forEach(customer => {
            notifications.push({
                title,
                message,
                type: type || 'info',
                priority: priority || 'normal',
                customerId: customer.id,
                actionUrl,
                actionText,
                expiresAt,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        });
        // إدراج جميع الإشعارات دفعة واحدة
        await Notification.bulkCreate(notifications);
    }

    // وضع الإشعار كمقروء
    async markAsRead(req, res) {
        try {
            const notification = await Notification.findByPk(req.params.id);
            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.markAsRead();

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع الإشعار كمقروء' });
            }

            req.flash('success', 'تم وضع الإشعار كمقروء');
            res.redirect('/notifications');
        } catch (error) {
            logger.error('Error marking notification as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // وضع جميع الإشعارات كمقروءة
    async markAllAsRead(req, res) {
        try {
            const { userType, userId } = req.body;

            if (!userType || !userId) {
                return res.status(400).json({ success: false, message: 'بيانات غير صحيحة' });
            }

            await Notification.markAllAsRead(userId, userType);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم وضع جميع الإشعارات كمقروءة' });
            }

            req.flash('success', 'تم وضع جميع الإشعارات كمقروءة');
            res.redirect('/notifications');
        } catch (error) {
            logger.error('Error marking all notifications as read:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // حذف الإشعار
    async delete(req, res) {
        try {
            const notification = await Notification.findByPk(req.params.id);
            if (!notification) {
                return res.status(404).json({ success: false, message: 'الإشعار غير موجود' });
            }

            await notification.destroy();

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.json({ success: true, message: 'تم حذف الإشعار بنجاح' });
            }

            req.flash('success', 'تم حذف الإشعار بنجاح');
            res.redirect('/notifications');
        } catch (error) {
            logger.error('Error deleting notification:', error);

            if (req.xhr || req.headers.accept?.includes('application/json')) {
                return res.status(500).json({ success: false, message: 'حدث خطأ' });
            }

            res.status(500).render('error', { error });
        }
    }

    // عرض تفاصيل الإشعار
    async show(req, res) {
        try {
            const notification = await Notification.findByPk(req.params.id, {
                include: [
                    { model: Customer, as: 'customer', required: false },
                    { model: Admin, as: 'admin', required: false }
                ]
            });

            if (!notification) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'الإشعار غير موجود' }
                });
            }

            res.render('notifications/show', { notification });
        } catch (error) {
            logger.error('Error showing notification:', error);
            res.status(500).render('error', { error });
        }
    }

    // API: جلب عدد الإشعارات غير المقروءة
    async getUnreadCount(req, res) {
        try {
            // استخدام معاملات افتراضية إذا لم تكن موجودة
            const { userType = 'admin', userId = 1 } = req.query;

            // إذا كان المستخدم مسجل دخول، استخدم بياناته
            if (req.user) {
                const actualUserId = req.user.id;
                const actualUserType = req.user.userType || 'admin';
                const count = await Notification.getUnreadCount(actualUserId, actualUserType);
                return res.json({ success: true, count });
            }

            // إذا لم يكن هناك مستخدم مسجل، استخدم القيم الافتراضية
            const count = await Notification.getUnreadCount(userId, userType);
            res.json({ success: true, count });
        } catch (error) {
            logger.error('Error getting unread count:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // API: جلب الإشعارات للمستخدم
    async getUserNotifications(req, res) {
        try {
            const { userType, userId } = req.query;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const offset = (page - 1) * limit;

            if (!userType || !userId) {
                return res.status(400).json({ success: false, message: 'بيانات غير صحيحة' });
            }

            const whereClause = {};
            if (userType === 'customer') {
                whereClause.customerId = userId;
            } else if (userType === 'admin') {
                whereClause.adminId = userId;
            }

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: {
                    ...whereClause,
                    expiresAt: {
                        [Op.or]: [
                            null,
                            { [Op.gt]: new Date() }
                        ]
                    }
                },
                order: [
                    ['priority', 'DESC'],
                    ['createdAt', 'DESC']
                ],
                limit,
                offset
            });

            res.json({
                success: true,
                notifications,
                totalCount: count,
                currentPage: page,
                totalPages: Math.ceil(count / limit)
            });
        } catch (error) {
            logger.error('Error getting user notifications:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ' });
        }
    }

    // عرض إشعارات المدير الحالي
    async adminIndex(req, res) {
        try {
            const adminId = req.user.id;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 15;
            const offset = (page - 1) * limit;

            const { count, rows: notifications } = await Notification.findAndCountAll({
                where: {
                    adminId: adminId,
                },
                order: [['createdAt', 'DESC']],
                limit,
                offset
            });

            const totalPages = Math.ceil(count / limit);

            res.render('admin/notifications/index', {
                notifications,
                count,
                totalPages,
                currentPage: page
            });
        } catch (error) {
            logger.error('Error loading admin notifications:', error);
            res.status(500).render('error', { error });
        }
    }

    async getAdminNotifications(req, res){
       try {
        const adminId = req.user.id;    
            const notifications = await Notification.findAll({ 
                where: {adminId: adminId, readAt: null },
                order: [['createdAt', 'DESC']]});
            res.json({ success: true, notifications });
        } catch (error) {
            res.status(500).json({ success: false, message: 'خطأ في تحميل الإشعارات' });
        }
    }
}

module.exports = new NotificationsController();
