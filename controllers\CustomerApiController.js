const { Customer, DeliveryPerson, Product, Order, OrderDetail, Category } = require('../models');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Op } = require('sequelize');
const axios = require('axios');
const NodeGeocoder = require('node-geocoder');

class CustomerApiController {
    
    /**
     * تسجيل دخول العميل
     * POST /api/customers/login
     */
   async login(req, res) {
    try {
        const { name, password } = req.body;

        if (!name || !password) {
            return res.status(400).json({
                success: false,
                message: 'البريد الإلكتروني أو رقم الهاتف وكلمة المرور مطلوبان',
                data: null
            });
        }

        const customer = await Customer.findOne({
            where: {
                [Op.or]: [
                    { name },
                    { phoneNumber: name }
                ]
            }
        });

        const driver = await DeliveryPerson.findOne({
            where: {
                [Op.or]: [
                    { username: name },
                    { phoneNumber: name }
                ]
            }
        });
        if (!customer && !driver) {
            return res.status(401).json({
                success: false,
                message: 'بيانات تسجيل الدخول غير صحيحة',
                data: null
            });
        }
        const isValidPassword = customer ? await customer.validatePassword(password) : false;
        const isValidPassworddelivery = driver ? await bcrypt.compare(password, driver.password) : false;
        if (!isValidPassword && !isValidPassworddelivery) {
            return res.status(401).json({
                success: false,
                message: 'بيانات تسجيل الدخول غير صحيحة',
                data: null
            });
        }

        let access_token = null;

        if (customer && isValidPassword) {
            access_token = jwt.sign(
                {
                    customerId: customer.id,
                    name: customer.name,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );
        } else if (driver && isValidPassworddelivery) {
            access_token = jwt.sign(
                {
                    driverId: driver.id,
                    name: driver.name,
                    type: 'driver'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );
        }

        res.json({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            data: {
                access_token,
                type: customer ? 'customer' : 'driver'
            }
        });

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
}


    /**
     * تسجيل عميل جديد
     * POST /api/customers/register
     */
    async register(req, res) {
        try {
            const { name, email, phone, password, address, areaId } = req.body;

            // التحقق من البيانات المطلوبة
            if (!name || !email || !phone || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'جميع البيانات الأساسية مطلوبة',
                    data: null
                });
            }

            // التحقق من عدم وجود العميل مسبقاً
            const existingCustomer = await Customer.findOne({
                where: {
                    [Op.or]: [
                        { email: email },
                        { phone: phone }
                    ]
                }
            });

            if (existingCustomer) {
                return res.status(409).json({
                    success: false,
                    message: 'البريد الإلكتروني أو رقم الهاتف مستخدم مسبقاً',
                    data: null
                });
            }

            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(password, 10);

            // إنشاء العميل الجديد
            const customer = await Customer.create({
                name,
                email,
                phone,
                password: hashedPassword,
                address,
                areaId
            });

            // إنشاء JWT token
            const token = jwt.sign(
                { 
                    customerId: customer.id,
                    email: customer.email,
                    type: 'customer'
                },
                process.env.JWT_SECRET || 'your-secret-key',
                { expiresIn: '30d' }
            );

            res.status(201).json({
                success: true,
                message: 'تم إنشاء الحساب بنجاح',
                data: {
                    token,
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        email: customer.email,
                        phone: customer.phone,
                        address: customer.address,
                        areaId: customer.areaId,
                        createdAt: customer.createdAt
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تسجيل العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على بيانات العميل
     * GET /api/customers/profile
     */
    async getProfile(req, res) {
        try {
            const customerId = req.customer.id;

            const customer = await Customer.findByPk(customerId, {
                attributes: ['id', 'name', 'barcode', 'image', 'phoneNumber', 'address', 'city', 'region', 'notes']
            });

            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            const ordercount = await Order.count({
                where: {
                    customerId: customer.id
                }
            });

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: {
                    id: customer.id,
                    name: customer.name,
                    barcode: customer.barcode,
                    image: customer.image,
                    phoneNumber: customer.phoneNumber,
                    address: customer.address,
                    city: customer.city,
                    region: customer.region,
                    notes: customer.notes,
                    orderCount: ordercount
                }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تحديث بيانات العميل
     * PUT /api/customers/profile
     */
    /*async updateProfile(req, res) {
        try {
            const customerId = req.customer.id;
            const {
                name,
                barcode,
                phoneNumber,
                address,
                city,
                region,
                latitude,
                longitude,
                notes,
                discountRate,
                status
            } = req.body;

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // إذا تم رفع صورة، نحفظ اسم الملف
            let imagePath = customer.image;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            await customer.update({
                name: name ?? customer.name,
                barcode: barcode ?? customer.barcode,
                phoneNumber: phoneNumber ?? customer.phoneNumber,
                image: imagePath,
                address: address ?? customer.address,
                city: city ?? customer.city,
                region: region ?? customer.region,
                latitude: latitude ?? customer.latitude,
                longitude: longitude ?? customer.longitude,
                notes: notes ?? customer.notes,
                discountRate: discountRate ?? customer.discountRate,
                status: status ?? customer.status,
            });

            res.json({
                success: true,
                message: 'تم تحديث البيانات بنجاح',
                data: {
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        phoneNumber: customer.phoneNumber,
                        image: customer.image,
                        address: customer.address,
                        city: customer.city,
                        region: customer.region,
                        latitude: customer.latitude,
                        longitude: customer.longitude,
                        notes: customer.notes,
                        discountRate: customer.discountRate,
                        status: customer.status,
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }*/

  
    // دالة لجلب المدينة والمنطقة من الإحداثيات
    /*async getCityAndRegion(lat, lon) {
        try {
            const response = await axios.get('https://nominatim.openstreetmap.org/reverse', {
                params: {
                    lat,
                    lon,
                    format: 'json',
                    addressdetails: 1,
                },
                headers: {
                    'User-Agent': 'Node.js App'
                }
            });
            const alladdress = response.data.address;
            const address = response.data.display_name;
            const city = alladdress.city || alladdress.town || alladdress.village || '';
            const region = alladdress.state || '';

            return { address,city, region };
        } catch (error) {
            console.error('فشل في جلب المدينة والمنطقة:', error.message);
            return { city: '', region: '' };
        }
    }*/



    async getCityAndRegion(lat, lon) {
        try {
            const geocoder = NodeGeocoder({
    provider: 'openstreetmap'
    });
            const res = await geocoder.reverse({ lat, lon });

            if (res.length === 0) return { city: '', region: '' };

            const location = res[0];

            const city =
                location.city ||
                location.town ||
                location.village ||
                location.hamlet ||
                '';

            const region =
                location.state ||
                location.region ||
                location.county ||
                '';

            return { city, region };
        } catch (error) {
            console.error('فشل في جلب المدينة والمنطقة:', error.message);
            return { city: '', region: '' };
        }
    }

    async updateProfile(req, res) {
        try {
            console.log('Request body:', req.body);
            const customerId = req.customer.id;
            let {
                name,
                barcode,
                phoneNumber,
                address,
                city,
                region,
                latitude,
                longitude,
                notes,
                discountRate,
                status
            } = req.body;

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // إذا تم رفع صورة، نحفظ اسم الملف
            let imagePath = customer.image;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // إذا لم يتم إرسال city و region ولكن تم إرسال latitude و longitude
            if (latitude && longitude) {
                const location = await this.getCityAndRegion(latitude, longitude);
                address = address || location.address;
                city = city || location.city;
                region = region || location.region;
            }

           await customer.update({
                name: name ?? customer.name,
                barcode: barcode ?? customer.barcode,
                phoneNumber: phoneNumber ?? customer.phoneNumber,
                image: imagePath,
                address: address ?? customer.address,
                city: city ?? customer.city,
                region: region ?? customer.region,
                latitude: latitude ?? customer.latitude,
                longitude: longitude ?? customer.longitude,
                notes: notes ?? customer.notes,
                discountRate: discountRate ?? customer.discountRate,
                status: status ?? customer.status,
            });

            res.json({
                success: true,
                message: 'تم تحديث البيانات بنجاح',
                data: {
                    customer: {
                        id: customer.id,
                        name: customer.name,
                        phoneNumber: customer.phoneNumber,
                        image: customer.image,
                        address: customer.address,
                        city: city,
                        region: region,
                        latitude: customer.latitude,
                        longitude: customer.longitude,
                        notes: customer.notes,
                        discountRate: customer.discountRate,
                        status: customer.status,
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث بيانات العميل:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تغيير كلمة المرور
     * PUT /api/customers/change-password
     */
    async changePassword(req, res) {
        try {
            const customerId = req.customer.id;
            const { currentPassword, newPassword } = req.body;

            if (!currentPassword || !newPassword) {
                return res.status(400).json({
                    success: false,
                    message: 'كلمة المرور الحالية والجديدة مطلوبتان',
                    data: null
                });
            }

            const customer = await Customer.findByPk(customerId);
            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'العميل غير موجود',
                    data: null
                });
            }

            // التحقق من كلمة المرور الحالية
            const isValidPassword = await bcrypt.compare(currentPassword, customer.password);
            if (!isValidPassword) {
                return res.status(401).json({
                    success: false,
                    message: 'كلمة المرور الحالية غير صحيحة',
                    data: null
                });
            }

            // تشفير كلمة المرور الجديدة
            const hashedPassword = await bcrypt.hash(newPassword, 10);

            // تحديث كلمة المرور
            await customer.update({ password: hashedPassword });

            res.json({
                success: true,
                message: 'تم تغيير كلمة المرور بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تغيير كلمة المرور:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * تسجيل الخروج
     * POST /api/customers/logout
     */
    async logout(req, res) {
        try {
            // في JWT لا نحتاج لحذف شيء من الخادم
            // يمكن للتطبيق حذف التوكن محلياً
            res.json({
                success: true,
                message: 'تم تسجيل الخروج بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    
}

module.exports = new CustomerApiController();
