
<!DOCTYPE html>
<html>
<head>
    <title>اختبار إشعارات Firebase</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>اختبار إشعارات Firebase</h1>
    <button id="testBtn">اختبار التوكن</button>
    <div id="result"></div>
    
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>
    <script src="/firebase-config.js"></script>
    
    <script>
        document.getElementById('testBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'جاري الاختبار...';
            
            try {
                // طلب إذن الإشعارات
                const permission = await Notification.requestPermission();
                if (permission !== 'granted') {
                    throw new Error('تم رفض إذن الإشعارات');
                }
                
                // إصلاح مشاكل التوكن
                const token = await window.fixTokenIssues();
                
                if (token) {
                    resultDiv.innerHTML = `
                        <h3>✅ نجح الاختبار!</h3>
                        <p><strong>التوكن:</strong> ${token.substring(0, 50)}...</p>
                        <p>يمكنك الآن استخدام هذا التوكن في التطبيق</p>
                    `;
                } else {
                    throw new Error('فشل في الحصول على التوكن');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ فشل الاختبار</h3>
                    <p><strong>الخطأ:</strong> ${error.message}</p>
                    <p>راجع Console للمزيد من التفاصيل</p>
                `;
                console.error('خطأ في الاختبار:', error);
            }
        });
    </script>
</body>
</html>
