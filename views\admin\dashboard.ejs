<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="connect-src 'self' https://nominatim.openstreetmap.org https://www.googleapis.com http://firebaseinstallations.googleapis.com https://fcmregistrations.googleapis.com;">

    <title>لوحة تحكم الإدارة - نظام إدارة المتجر</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #B2CD9C;
            --secondary-color: #8FBC8F;
            --accent-color: #7BA05B;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white) !important;
        }

        .container-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .welcome-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 1rem;
            font-weight: 500;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .action-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .action-card h5 {
            color: var(--text-dark);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 0.5rem;
            width: 100%;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .dropdown-menu {
            border: none;
            box-shadow: var(--shadow-lg);
            border-radius: 10px;
        }

        .dropdown-item:hover {
            background: var(--bg-light);
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                     <li class="nav-item">
                        <a class="nav-link" href="/admin/offers">
                            <i class="fas fa-tags me-1"></i>العروض
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-box-open me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders/pending">
                            <i class="fas fa-clock me-1"></i>الطلبات المعلقة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/drivers">
                            <i class="fas fa-truck me-1"></i>السائقين
                        </a>
                    </li>
                      <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                </ul>

                <div class="dropdown">
                    <button class="btn toggle-btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                         المدير 
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/notifications">
                            <i class="fas fa-user me-2"></i>الإشعارات
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                    <button id="enableNotifications" class="btn btn-primary btn-sm">
                        <i class="fas fa-bell me-1"></i>تفعيل الإشعارات
                    </button>
                    <div id="notificationStatus" style="display: none;" class="alert alert-success alert-sm mt-2">
                        <i class="fas fa-check-circle me-1"></i>الإشعارات مفعلة
                    </div>
                </div>
            </div>
        </div>
    </nav>
<div id="admin-notifications" style="position: fixed; top: 10px; right: 10px; z-index: 1000;"></div>

    <!-- Main Content -->
    <div class="container-main">
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2" style="color: var(--primary-color);">
                        <i class="fas fa-chart-line me-2"></i>
                        مرحباً بك في لوحة التحكم
                    </h1>
                    <p class="mb-0" style="color: var(--text-light);">
                        <i class="fas fa-calendar-alt me-2"></i>
                        إدارة شاملة ومتطورة لنظام المتجر
                    </p>
                </div>
                <div class="text-end">
                    <div class="badge bg-success fs-6 p-2">
                        <i class="fas fa-circle me-1"></i>
                        النظام يعمل بشكل طبيعي
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><%= customers || 0 %></div>
                <div class="stat-label">إجمالي العملاء</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-number"><%= products || 0 %></div>
                <div class="stat-label">إجمالي المنتجات</div>
            </div>

            <!-- إجمالي الطلبات -->
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #36D1DC, #5B86E5);">
                    <i class="fas fa-box-open"></i> <!-- رمز يعبر عن الطلبات بشكل عام -->
                </div>
                <div class="stat-number"><%= orders || 0 %></div>
                <div class="stat-label">إجمالي الطلبات</div>
            </div>

            <!-- الطلبات المعلقة -->
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #F7971E, #FFD200);">
                    <i class="fas fa-clock"></i> <!-- رمز يشير إلى "انتظار" أو "معلّق" -->
                </div>
                <div class="stat-number"><%= orderspending || 0 %></div>
                <div class="stat-label">الطلبات المعلقة</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stat-number"><%= categories || 0 %></div>
                <div class="stat-label">إجمالي الفئات</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="stat-number"><%= deliveryPeople || 0 %></div>
                <div class="stat-label">مندوبي التوصيل</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #e67e22, #d35400);">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-number">0</div>
                <div class="stat-label">الإشعارات الجديدة</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="actions-grid">
            <div class="action-card">
                <h5>
                    <i class="fas fa-plus-circle me-2" style="color: var(--primary-color);"></i>
                    إجراءات سريعة
                </h5>
                <a href="/admin/products/create" class="btn btn-custom">
                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                </a>
                <a href="/admin/categories/create" class="btn btn-custom">
                    <i class="fas fa-tags me-2"></i>إضافة فئة جديدة
                </a>
                <a href="/admin/customers" class="btn btn-custom">
                    <i class="fas fa-users me-2"></i>عرض العملاء
                </a>
            </div>

            <div class="action-card">
                <h5>
                    <i class="fas fa-chart-bar me-2" style="color: var(--primary-color);"></i>
                    إحصائيات اليوم
                </h5>
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 style="color: var(--primary-color);">0</h4>
                            <small class="text-muted">طلبات جديدة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 style="color: var(--secondary-color);">0</h4>
                        <small class="text-muted">مبيعات اليوم</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 style="color: var(--accent-color);">0</h4>
                            <small class="text-muted">عملاء جدد</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 style="color: var(--text-light);">0</h4>
                        <small class="text-muted">منتجات جديدة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
    document.querySelector('.toggle-btn').addEventListener('click', function() {
        this.nextElementSibling.classList.toggle('show');
    });
</script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>

<!-- 2. كود تهيئة Firebase والرسائل -->
<script>
  const firebaseConfig = {
    apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
    authDomain: "company-firebase-77daf.firebaseapp.com",
    projectId: "company-firebase-77daf",
    storageBucket: "company-firebase-77daf.appspot.com",
    messagingSenderId: "76415949840",
    appId: "1:76415949840:web:d74b24b187a1abf392fe95",
    measurementId: "G-YRPEE91G0C"
  };

  // تهيئة Firebase
  firebase.initializeApp(firebaseConfig);
  const messaging = firebase.messaging();

  // عند النقر على زر تفعيل الإشعارات
  document.getElementById('enableNotifications').addEventListener('click', async () => {
    const enableBtn = document.getElementById('enableNotifications');
    const statusDiv = document.getElementById('notificationStatus');

    // إظهار حالة التحميل
    enableBtn.disabled = true;
    enableBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التفعيل...';

    try {
      console.log("🔄 بدء عملية تفعيل الإشعارات...");

      // فحص دعم المتصفح
      if (!('Notification' in window)) {
        throw new Error('هذا المتصفح لا يدعم الإشعارات');
      }

      if (!('serviceWorker' in navigator)) {
        throw new Error('هذا المتصفح لا يدعم Service Workers');
      }

      if (!firebase || !firebase.messaging) {
        throw new Error('Firebase Messaging غير محمل بشكل صحيح');
      }

      console.log("✅ فحص دعم المتصفح مكتمل");

      // طلب إذن الإشعارات
      console.log("🔔 طلب إذن الإشعارات...");
      const permission = await Notification.requestPermission();

      if (permission !== 'granted') {
        throw new Error('تم رفض إذن الإشعارات. يرجى السماح بالإشعارات من إعدادات المتصفح.');
      }

      console.log("✅ تم منح إذن الإشعارات");

      // تسجيل Service Worker
      console.log("⚙️ تسجيل Service Worker...");
      let registration;
      try {
        // إلغاء تسجيل Service Workers القديمة أولاً
        const existingRegistrations = await navigator.serviceWorker.getRegistrations();
        for (const reg of existingRegistrations) {
          if (reg.scope.includes('firebase-messaging-sw')) {
            await reg.unregister();
            console.log("🗑️ تم إلغاء تسجيل Service Worker قديم");
          }
        }

        registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
          scope: '/',
          updateViaCache: 'none' // تجنب مشاكل التخزين المؤقت
        });

        console.log("✅ تم تسجيل Service Worker بنجاح");

        // انتظار حتى يصبح Service Worker نشطاً
        await navigator.serviceWorker.ready;
        console.log("✅ Service Worker جاهز");

      } catch (swError) {
        console.error("❌ فشل تسجيل Service Worker:", swError);
        throw new Error(`فشل في تسجيل Service Worker: ${swError.message}`);
      }

      // الحصول على FCM Token مع معالجة محسنة
      console.log("🔑 الحصول على FCM Token...");
      try {
        // محاولة حذف التوكن القديم أولاً
        try {
          await messaging.deleteToken();
          console.log("🗑️ تم حذف التوكن القديم");
        } catch (deleteError) {
          console.log("ℹ️ لا يوجد توكن قديم للحذف");
        }

        // الحصول على توكن جديد
        const currentToken = await messaging.getToken({
          vapidKey: 'BAeiibK5g-rcijaiIb-kC1qWVzeLEWWIvYsbfj6xHx162ETMsPF8lPuq9mRRVkvBn6NMSMnZYRYtP4-5sbe8vRk',
          serviceWorkerRegistration: registration
        });

        if (!currentToken) {
          throw new Error('فشل في توليد التوكن. تحقق من إعدادات المتصفح والشبكة.');
        }

        console.log("✅ تم الحصول على FCM Token:", currentToken.substring(0, 20) + "...");

        // إرسال التوكن للسيرفر
        console.log("💾 حفظ التوكن في السيرفر...");
        const response = await fetch('/admin/savetoken', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({ token: currentToken })
        });

        if (!response.ok) {
          throw new Error(`خطأ في الخادم: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        if (!result.success) {
          throw new Error(result.message || 'فشل في حفظ التوكن');
        }

        console.log("✅ تم حفظ التوكن في السيرفر بنجاح");

        // إظهار حالة النجاح
        enableBtn.style.display = 'none';
        statusDiv.style.display = 'block';

        // اختبار الإشعار
        console.log("🧪 إرسال إشعار تجريبي...");
        try {
          const testResponse = await fetch('/admin/test-notification', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });

          if (testResponse.ok) {
            console.log("✅ تم إرسال إشعار تجريبي");
          }
        } catch (testError) {
          console.log("ℹ️ لم يتم إرسال إشعار تجريبي:", testError.message);
        }

        alert('تم تفعيل الإشعارات بنجاح! ستتلقى إشعاراً تجريبياً قريباً.');

      } catch (tokenError) {
        console.error("❌ فشل في الحصول على التوكن:", tokenError);

        let errorMessage = 'فشل في إعداد الإشعارات: ';

        // معالجة أخطاء محددة
        switch (tokenError.code) {
          case 'messaging/unsupported-browser':
            errorMessage += 'هذا المتصفح لا يدعم إشعارات Firebase';
            break;
          case 'messaging/permission-blocked':
            errorMessage += 'تم حظر الإشعارات. يرجى تفعيلها من إعدادات المتصفح';
            break;
          case 'messaging/vapid-key-required':
            errorMessage += 'خطأ في إعداد مفاتيح الإشعارات';
            break;
          case 'messaging/token-unsubscribe-failed':
            errorMessage += 'فشل في إلغاء الاشتراك السابق';
            break;
          default:
            errorMessage += tokenError.message || 'خطأ غير معروف';
        }

        throw new Error(errorMessage);
      }

    } catch (err) {
      console.error("❌ حدث خطأ عام:", err);

      // إعادة تعيين حالة الزر
      enableBtn.disabled = false;
      enableBtn.innerHTML = '<i class="fas fa-bell me-1"></i>تفعيل الإشعارات';

      alert(err.message || 'حدث خطأ غير متوقع');
    }
  });

  // استقبال الإشعارات داخل الصفحة
  messaging.onMessage((payload) => {
    console.log('📥 تم استقبال إشعار في الصفحة:', payload);

    const notification = document.createElement('div');
    notification.classList.add('alert', 'alert-info', 'alert-dismissible', 'fade', 'show');
    notification.innerHTML = `
      <strong>${payload.notification.title}</strong><br>
      ${payload.notification.body}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.getElementById('admin-notifications').appendChild(notification);
  });
</script>
</body>
</html>