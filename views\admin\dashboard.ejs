<!-- Main Content -->
    <!-- Welcome Section -->
    <div class="welcome-card">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2" style="color: var(--primary-color);">
                    <i class="fas fa-chart-line me-2"></i>
                    مرحباً بك في لوحة التحكم
                </h1>
                <p class="mb-0" style="color: var(--text-light);">
                    <i class="fas fa-calendar-alt me-2"></i>
                    إدارة شاملة ومتطورة لنظام المتجر
                </p>
            </div>
            <div class="text-end">
                <div class="badge bg-success fs-6 p-2 mb-2">
                    <i class="fas fa-circle me-1"></i>
                    النظام يعمل بشكل طبيعي
                </div>
                <!-- زر إعادة تعيين رسالة الإشعارات للاختبار -->
                <div>
                    <button class="btn btn-outline-secondary btn-sm" onclick="resetNotificationPrompt()" title="إعادة تعيين رسالة تفعيل الإشعارات">
                        <i class="fas fa-redo me-1"></i>
                        إعادة تعيين الإشعارات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number"><%= customers || 0 %></div>
            <div class="stat-label">إجمالي العملاء</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                <i class="fas fa-box"></i>
            </div>
            <div class="stat-number"><%= products || 0 %></div>
            <div class="stat-label">إجمالي المنتجات</div>
        </div>

        <!-- إجمالي الطلبات -->
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #36D1DC, #5B86E5);">
                <i class="fas fa-box-open"></i> <!-- رمز يعبر عن الطلبات بشكل عام -->
            </div>
            <div class="stat-number"><%= orders || 0 %></div>
            <div class="stat-label">إجمالي الطلبات</div>
        </div>

        <!-- الطلبات المعلقة -->
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #F7971E, #FFD200);">
                <i class="fas fa-clock"></i> <!-- رمز يشير إلى "انتظار" أو "معلّق" -->
            </div>
            <div class="stat-number"><%= orderspending || 0 %></div>
            <div class="stat-label">الطلبات المعلقة</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad);">
                <i class="fas fa-tags"></i>
            </div>
            <div class="stat-number"><%= categories || 0 %></div>
            <div class="stat-label">إجمالي الفئات</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #1abc9c, #16a085);">
                <i class="fas fa-truck"></i>
            </div>
            <div class="stat-number"><%= deliveryPeople || 0 %></div>
            <div class="stat-label">مندوبي التوصيل</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon" style="background: linear-gradient(135deg, #e67e22, #d35400);">
                <i class="fas fa-bell"></i>
            </div>
            <div class="stat-number" id="dashboardNotificationCount">0</div>
            <div class="stat-label">الإشعارات الجديدة</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-grid">
        <div class="action-card">
            <h5>
                <i class="fas fa-plus-circle me-2" style="color: var(--primary-color);"></i>
                إجراءات سريعة
            </h5>
            <a href="/admin/products/create" class="btn btn-custom">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </a>
            <a href="/admin/categories/create" class="btn btn-custom">
                <i class="fas fa-tags me-2"></i>إضافة فئة جديدة
            </a>
            <a href="/admin/customers" class="btn btn-custom">
                <i class="fas fa-users me-2"></i>عرض العملاء
            </a>
        </div>

        <div class="action-card">
            <h5>
                <i class="fas fa-chart-bar me-2" style="color: var(--primary-color);"></i>
                إحصائيات اليوم
            </h5>
            <div class="row text-center">
                <div class="col-6 mb-3">
                    <div class="border-end">
                        <h4 style="color: var(--primary-color);">0</h4>
                        <small class="text-muted">طلبات جديدة</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <h4 style="color: var(--secondary-color);">0</h4>
                    <small class="text-muted">مبيعات اليوم</small>
                </div>
                <div class="col-6">
                    <div class="border-end">
                        <h4 style="color: var(--accent-color);">0</h4>
                        <small class="text-muted">عملاء جدد</small>
                    </div>
                </div>
                <div class="col-6">
                    <h4 style="color: var(--text-light);">0</h4>
                    <small class="text-muted">منتجات جديدة</small>
                </div>
            </div>
        </div>
    </div>

<script>
// دالة إعادة تعيين رسالة تفعيل الإشعارات
function resetNotificationPrompt() {
    localStorage.removeItem('notificationPromptShown');
    localStorage.removeItem('notificationPermissionDenied');
    console.log('🔄 تم إعادة تعيين حالة رسالة الإشعارات');

    // إعادة تحميل الصفحة لإظهار الرسالة مرة أخرى
    if (confirm('سيتم إعادة تعيين حالة الإشعارات وإعادة تحميل الصفحة. هل تريد المتابعة؟')) {
        window.location.reload();
    }
}

// تحديث عداد الإشعارات في لوحة التحكم
document.addEventListener('DOMContentLoaded', function() {
    function updateDashboardNotificationCount() {
        if (window.adminNotifications) {
            const dashboardCount = document.getElementById('dashboardNotificationCount');
            if (dashboardCount) {
                dashboardCount.textContent = window.adminNotifications.notificationCount || 0;
            }
        }
    }

    // تحديث العداد كل 5 ثوان
    setInterval(updateDashboardNotificationCount, 5000);

    // تحديث فوري عند تحميل الصفحة
    setTimeout(updateDashboardNotificationCount, 1000);
});
</script>