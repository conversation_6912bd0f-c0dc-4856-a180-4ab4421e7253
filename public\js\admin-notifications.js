/**
 * نظام إدارة الإشعارات للمديرين
 * Admin Notification Management System
 */

class AdminNotificationSystem {
    constructor() {
        this.updateInterval = null;
        this.isInitialized = false;
        this.notificationSound = null;
        this.lastNotificationCount = 0;
        this.notificationsList = [];
        
        this.init();
    }

    /**
     * تهيئة النظام
     */
    async init() {
        try {
            console.log('🔄 تهيئة نظام الإشعارات...');
            
            // تحميل الصوت
            this.loadNotificationSound();
            
            // تحميل الإشعارات الأولية
            await this.loadNotificationsList();
            
            // بدء التحديث التلقائي
            this.startAutoUpdate();
            
            // إعداد معالجات الأحداث
            this.setupEventHandlers();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام الإشعارات بنجاح');
            
        } catch (error) {
            console.error('❌ فشل في تهيئة نظام الإشعارات:', error);
        }
    }

    /**
     * تحميل صوت الإشعار
     */
    loadNotificationSound() {
        try {
            this.notificationSound = new Audio('/sounds/notification.mp3');
            this.notificationSound.volume = 0.5;
        } catch (error) {
            console.warn('⚠️ لا يمكن تحميل صوت الإشعار:', error);
        }
    }

    /**
     * تحميل قائمة الإشعارات
     */
    async loadNotificationsList() {
        try {
            const response = await fetch('/admin/notifications/get', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.notificationsList = data.notifications || [];
                this.updateNotificationDisplay();
                this.updateNotificationCount();

                // التحقق من وجود إشعارات جديدة
                this.checkForNewNotifications();

            } else {
                throw new Error(data.message || 'فشل في تحميل الإشعارات');
            }

        } catch (error) {
            console.error('❌ خطأ في تحميل الإشعارات:', error);
            this.showError('فشل في تحميل الإشعارات');
        }
    }

    /**
     * تحديث عرض الإشعارات
     */
    updateNotificationDisplay() {
        const notificationsContainer = document.getElementById('notificationsList');
        if (!notificationsContainer) return;

        if (this.notificationsList.length === 0) {
            notificationsContainer.innerHTML = `
                <div class="dropdown-item text-center text-muted py-3">
                    <i class="fas fa-bell-slash mb-2"></i>
                    <div>لا توجد إشعارات</div>
                </div>
            `;
            return;
        }

        const notificationsHTML = this.notificationsList.slice(0, 10).map(notification => {
            const timeAgo = this.getTimeAgo(notification.createdAt);
            const isUnread = !notification.readAt;
            
            return `
                <div class="dropdown-item notification-item ${isUnread ? 'unread' : ''}" 
                     data-notification-id="${notification.id}"
                     onclick="handleNotificationClick(${notification.id}, '${notification.actionUrl || '#'}')">
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-3">
                            <i class="fas ${this.getNotificationIcon(notification.type)}"></i>
                        </div>
                        <div class="notification-content flex-grow-1">
                            <div class="notification-title fw-bold">${notification.title}</div>
                            <div class="notification-message text-muted small">${notification.message}</div>
                            <div class="notification-time text-muted small">
                                <i class="fas fa-clock me-1"></i>${timeAgo}
                            </div>
                        </div>
                        ${isUnread ? '<div class="notification-badge"></div>' : ''}
                    </div>
                </div>
            `;
        }).join('');

        notificationsContainer.innerHTML = notificationsHTML;
    }

    /**
     * تحديث عداد الإشعارات
     */
    updateNotificationCount() {
        const unreadCount = this.notificationsList.filter(n => !n.readAt).length;

        // تحديث العداد في الشريط العلوي
        const countElement = document.getElementById('notificationBadge');
        if (countElement) {
            if (unreadCount > 0) {
                countElement.textContent = unreadCount > 99 ? '99+' : unreadCount;
                countElement.classList.remove('d-none');
            } else {
                countElement.classList.add('d-none');
            }
        }

        // تحديث العداد في لوحة التحكم
        const dashboardCountElement = document.getElementById('dashboardNotificationCount');
        if (dashboardCountElement) {
            dashboardCountElement.textContent = unreadCount;
        }
    }

    /**
     * التحقق من وجود إشعارات جديدة
     */
    checkForNewNotifications() {
        const currentUnreadCount = this.notificationsList.filter(n => !n.readAt).length;
        
        if (currentUnreadCount > this.lastNotificationCount) {
            // إشعار جديد وصل
            this.showNewNotificationAlert();
            this.playNotificationSound();
        }
        
        this.lastNotificationCount = currentUnreadCount;
    }

    /**
     * عرض تنبيه الإشعار الجديد
     */
    showNewNotificationAlert() {
        const latestNotification = this.notificationsList.find(n => !n.readAt);
        if (!latestNotification) return;

        // إنشاء Toast notification
        this.showToast(
            latestNotification.title,
            latestNotification.message,
            'info',
            5000
        );

        // تأثير بصري على أيقونة الجرس
        const bellIcon = document.querySelector('.notification-bell');
        if (bellIcon) {
            bellIcon.classList.add('animate__animated', 'animate__swing');
            setTimeout(() => {
                bellIcon.classList.remove('animate__animated', 'animate__swing');
            }, 1000);
        }
    }

    /**
     * تشغيل صوت الإشعار
     */
    playNotificationSound() {
        if (this.notificationSound) {
            this.notificationSound.play().catch(error => {
                console.warn('⚠️ لا يمكن تشغيل صوت الإشعار:', error);
            });
        }
    }

    /**
     * عرض Toast notification
     */
    showToast(title, message, type = 'info', duration = 5000) {
        // إنشاء container إذا لم يكن موجوداً
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // إنشاء Toast
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <div class="fw-bold">${title}</div>
                        <div class="small">${message}</div>
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: duration
        });
        
        toast.show();

        // إزالة العنصر بعد الإخفاء
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoUpdate() {
        // تحديث كل 30 ثانية
        this.updateInterval = setInterval(() => {
            this.loadNotificationsList();
        }, 30000);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // معالج النقر على الجرس
        const notificationBell = document.querySelector('.notification-bell');
        if (notificationBell) {
            notificationBell.addEventListener('click', () => {
                this.loadNotificationsList();
            });
        }

        // معالج تحديد جميع الإشعارات كمقروءة
        const markAllReadBtn = document.getElementById('mark-all-read');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', () => {
                this.markAllAsRead();
            });
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    async markAllAsRead() {
        try {
            const response = await fetch('/admin/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                await this.loadNotificationsList();
                this.showToast('تم بنجاح', 'تم تحديد جميع الإشعارات كمقروءة', 'success');
            }
        } catch (error) {
            console.error('خطأ في تحديد الإشعارات كمقروءة:', error);
            this.showError('فشل في تحديد الإشعارات كمقروءة');
        }
    }

    /**
     * الحصول على أيقونة الإشعار حسب النوع
     */
    getNotificationIcon(type) {
        const icons = {
            'order': 'fa-shopping-cart',
            'delivery': 'fa-truck',
            'customer': 'fa-user',
            'system': 'fa-cog',
            'warning': 'fa-exclamation-triangle',
            'success': 'fa-check-circle',
            'info': 'fa-info-circle',
            'error': 'fa-times-circle'
        };
        return icons[type] || 'fa-bell';
    }

    /**
     * حساب الوقت المنقضي
     */
    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'الآن';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
        return `${Math.floor(diffInSeconds / 86400)} يوم`;
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showToast('خطأ', message, 'danger', 5000);
    }

    /**
     * تنظيف الموارد
     */
    destroy() {
        this.stopAutoUpdate();
        this.isInitialized = false;
        console.log('🧹 تم تنظيف موارد نظام الإشعارات');
    }
}

/**
 * معالج النقر على الإشعار
 */
async function handleNotificationClick(notificationId, actionUrl) {
    try {
        // تحديد الإشعار كمقروء
        await markNotificationAsRead(notificationId);
        
        // الانتقال إلى الرابط إذا كان موجوداً
        if (actionUrl && actionUrl !== '#') {
            window.location.href = actionUrl;
        }
    } catch (error) {
        console.error('خطأ في معالجة النقر على الإشعار:', error);
    }
}

/**
 * تحديد الإشعار كمقروء
 */
async function markNotificationAsRead(notificationId) {
    try {
        const response = await fetch(`/admin/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok && window.adminNotifications) {
            await window.adminNotifications.loadNotificationsList();
        }
    } catch (error) {
        console.error('خطأ في تحديد الإشعار كمقروء:', error);
    }
}

// تصدير الكلاس للاستخدام العام
window.AdminNotificationSystem = AdminNotificationSystem;
