/**
 * Admin Notification System - Professional JavaScript Organization
 * Handles Firebase Cloud Messaging and notification management
 */

class AdminNotificationSystem {
    constructor() {
        this.messaging = null;
        this.isInitialized = false;
        this.isEnabled = false;
        this.notificationCount = 0;
        this.notifications = [];
        this.isUpdating = false;
        this.notificationsLoaded = false;
        this.updateInterval = null;
        this.currentToken = null;
        this.vapidKey = 'BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc';
        this.init();
    }

    async init() {
        try {
            console.log('🔔 تهيئة نظام الإشعارات للإدمن...');

            // Check if Firebase is loaded
            if (typeof firebase === 'undefined') {
                console.error('❌ Firebase غير محمل');
                this.updateNotificationStatus('error');
                return;
            }

            // Initialize Firebase messaging
            if (window.initializeFirebaseMessaging) {
                this.messaging = window.initializeFirebaseMessaging();
            } else {
                // Fallback
                firebase.initializeApp(window.FIREBASE_CONFIG || {
                    apiKey: "AIzaSyBdtHd9YCMgqvBs0KAVaen3Jg4M6x2mdhE",
                    authDomain: "company-firebase-77daf.firebaseapp.com",
                    projectId: "company-firebase-77daf",
                    storageBucket: "company-firebase-77daf.appspot.com",
                    messagingSenderId: "76415949840",
                    appId: "1:76415949840:web:d74b24b187a1abf392fe95"
                });
                this.messaging = firebase.messaging();
            }

            // Check notification permission
            await this.checkNotificationPermission();

            // Setup message handlers
            this.setupMessageHandlers();

            // Setup UI event handlers
            this.setupUIHandlers();

            // Load notifications list
            this.loadNotificationsList();

            // Update notifications every 30 seconds
            this.updateInterval = setInterval(() => {
                if (!this.isUpdating) {
                    this.loadNotificationsList();
                }
            }, 30000);

            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام الإشعارات بنجاح');

        } catch (error) {
            console.error('❌ فشل في تهيئة نظام الإشعارات:', error);
            this.updateNotificationStatus('error');
        }
    }

    async checkNotificationPermission() {
        if (!('Notification' in window)) {
            console.warn('⚠️ المتصفح لا يدعم الإشعارات');
            this.updateNotificationStatus('unsupported');
            return;
        }

        const permission = Notification.permission;
        console.log('🔐 حالة إذن الإشعارات:', permission);

        switch (permission) {
            case 'granted':
                this.isEnabled = true;
                this.updateNotificationStatus('enabled');
                this.hidePermissionAlert();
                break;
            case 'denied':
                this.isEnabled = false;
                this.updateNotificationStatus('denied');
                localStorage.setItem('notificationPromptShown', 'true');
                localStorage.setItem('notificationPermissionDenied', 'true');
                console.log('🔕 الإشعارات مرفوضة - لن يتم عرض رسالة التفعيل');
                break;
            case 'default':
                this.isEnabled = false;
                this.updateNotificationStatus('default');
                this.showPermissionAlert();
                break;
        }
    }

    async enableNotifications() {
        try {
            this.updateNotificationStatus('pending');

            let token;
            if (window.fixTokenIssues) {
                token = await window.fixTokenIssues();
            } else {
                // Fallback method
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                await navigator.serviceWorker.ready;

                token = await this.messaging.getToken({
                    vapidKey: this.vapidKey,
                    serviceWorkerRegistration: registration
                });
            }

            if (token) {
                await this.saveTokenToServer(token);
                this.updateNotificationStatus('enabled');
                this.hidePermissionAlert();
                console.log('✅ تم تفعيل الإشعارات بنجاح');

                // Show success notification
                this.showLocalNotification('تم تفعيل الإشعارات', 'سيتم إشعارك بالأحداث المهمة');
            } else {
                throw new Error('فشل في الحصول على التوكن');
            }
        } catch (error) {
            console.error('❌ فشل في تفعيل الإشعارات:', error);
            this.updateNotificationStatus('error');
            this.showError('فشل في تفعيل الإشعارات: ' + error.message);
        }
    }

    async saveTokenToServer(token) {
        const response = await fetch('/admin/savetoken', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ token })
        });

        if (!response.ok) {
            throw new Error('فشل في حفظ التوكن على الخادم');
        }

        const result = await response.json();
        if (!result.success) {
            throw new Error(result.message || 'فشل في حفظ التوكن');
        }
    }

    setupMessageHandlers() {
        try {
            // Handle foreground messages
            this.messaging.onMessage((payload) => {
                console.log('📨 تم استلام إشعار:', payload);
                this.handleIncomingNotification(payload);
            });

            // Setup token refresh monitoring
            this.setupTokenRefreshMonitoring();

        } catch (error) {
            console.error('❌ خطأ في إعداد معالجات الرسائل:', error);
        }
    }

    setupTokenRefreshMonitoring() {
        // Monitor token changes periodically
        setInterval(async () => {
            try {
                if (!this.messaging) return;

                const currentToken = await this.messaging.getToken({
                    vapidKey: this.vapidKey
                });

                if (currentToken && currentToken !== this.currentToken) {
                    console.log('🔄 تم اكتشاف توكن جديد');
                    this.currentToken = currentToken;
                    await this.saveTokenToServer(currentToken);
                    console.log('✅ تم تحديث التوكن بنجاح');
                }
            } catch (error) {
                console.error('❌ خطأ في مراقبة تحديث التوكن:', error);
            }
        }, 30 * 60 * 1000); // Check every 30 minutes
    }

    setupUIHandlers() {
        // Enable notifications button
        const enableBtn = document.getElementById('enableNotificationsBtn');
        if (enableBtn) {
            enableBtn.addEventListener('click', () => this.enableNotifications());
        }

        // Notification dropdown toggle
        const notificationDropdown = document.getElementById('notificationDropdown');
        if (notificationDropdown) {
            notificationDropdown.addEventListener('click', () => {
                if (!this.notificationsLoaded) {
                    this.loadNotificationsList();
                }
            });
        }

        // Mark all as read button
        const markAllReadBtn = document.getElementById('markAllReadBtn');
        if (markAllReadBtn) {
            markAllReadBtn.addEventListener('click', () => this.markAllAsRead());
        }
    }

    updateNotificationStatus(status) {
        const statusDot = document.getElementById('notificationStatusDot');
        const indicator = document.querySelector('.notification-status-indicator');
        
        if (statusDot) {
            statusDot.className = 'notification-status-dot';
            statusDot.classList.add(status);
        }
        
        if (indicator) {
            indicator.className = 'notification-status-indicator';
            indicator.classList.add(status);
        }

        // Update visibility based on status
        if (status === 'enabled') {
            if (statusDot) statusDot.style.display = 'none';
        } else if (status === 'pending') {
            if (statusDot) statusDot.style.display = 'block';
        }
    }

    showPermissionAlert() {
        // Only show on dashboard and if not shown before
        const isDashboard = window.location.pathname === '/admin/dashboard' || 
                           window.location.pathname === '/admin' ||
                           window.location.pathname === '/';
        
        const hasShown = localStorage.getItem('notificationPromptShown');
        const isDenied = localStorage.getItem('notificationPermissionDenied');
        
        if (!isDashboard || hasShown || isDenied) {
            return;
        }

        const alertHtml = `
            <div id="permissionAlert" class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-bell"></i>
                <span>تفعيل الإشعارات للحصول على التحديثات الفورية</span>
                <button type="button" class="btn btn-sm btn-outline-warning" id="enableNotificationsBtn">
                    تفعيل الآن
                </button>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        const container = document.getElementById('admin-notifications');
        if (container) {
            container.innerHTML = alertHtml;
            
            // Mark as shown
            localStorage.setItem('notificationPromptShown', 'true');
            
            // Re-attach event listener
            const enableBtn = document.getElementById('enableNotificationsBtn');
            if (enableBtn) {
                enableBtn.addEventListener('click', () => this.enableNotifications());
            }
        }
    }

    hidePermissionAlert() {
        const alert = document.getElementById('permissionAlert');
        if (alert) {
            alert.remove();
        }
    }

    handleIncomingNotification(payload) {
        // Update notification count
        this.notificationCount++;
        this.updateNotificationBadge();

        // Show in-page notification
        this.showInPageNotification(payload);

        // Reload notifications list
        this.loadNotificationsList();
    }

    showInPageNotification(payload) {
        const notification = document.createElement('div');
        notification.classList.add('alert', 'alert-info', 'alert-dismissible', 'fade', 'show');
        notification.innerHTML = `
            <strong>${payload.notification.title}</strong><br>
            ${payload.notification.body}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.getElementById('admin-notifications');
        if (container) {
            container.appendChild(notification);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    }

    showLocalNotification(title, body) {
        if (this.isEnabled && 'Notification' in window) {
            new Notification(title, { body });
        }
    }

    showError(message) {
        console.error(message);
        // Could implement toast notification here
    }

    updateNotificationBadge() {
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            if (this.notificationCount > 0) {
                badge.textContent = this.notificationCount > 99 ? '99+' : this.notificationCount;
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        }
    }

    async loadNotificationsList() {
        if (this.isUpdating) return;
        
        this.isUpdating = true;
        
        try {
            const response = await fetch('/admin/notifications/api/admin');
            if (!response.ok) throw new Error('فشل في تحميل الإشعارات');
            
            const data = await response.json();
            if (data.success) {
                this.notifications = data.notifications;
                this.notificationCount = data.notifications.filter(n => !n.readAt).length;
                this.updateNotificationBadge();
                this.renderNotificationsList();
                this.notificationsLoaded = true;
            }
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        } finally {
            this.isUpdating = false;
        }
    }

    renderNotificationsList() {
        const container = document.getElementById('notificationsList');
        if (!container) return;

        if (this.notifications.length === 0) {
            container.innerHTML = '<div class="dropdown-item-text text-center text-muted">لا توجد إشعارات</div>';
            return;
        }

        const html = this.notifications.slice(0, 5).map(notification => `
            <div class="notification-item ${!notification.readAt ? 'unread' : ''}" 
                 onclick="markNotificationAsRead(${notification.id})">
                <div class="notification-icon">
                    <i class="fas fa-${this.getNotificationIcon(notification.type)}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">
                        <i class="fas fa-clock"></i>
                        ${this.formatTime(notification.createdAt)}
                    </div>
                </div>
                ${!notification.readAt ? '<div class="notification-dot"></div>' : ''}
            </div>
        `).join('<div class="dropdown-divider"></div>');

        container.innerHTML = html;
    }

    getNotificationIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'times-circle',
            'order': 'shopping-cart',
            'user': 'user',
            'system': 'cog'
        };
        return icons[type] || 'bell';
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return 'الآن';
        if (diff < 3600000) return `${Math.floor(diff / 60000)} دقيقة`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)} ساعة`;
        return `${Math.floor(diff / 86400000)} يوم`;
    }

    async markAllAsRead() {
        try {
            const response = await fetch('/admin/notifications/mark-all-read', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userType: 'admin', userId: 1 })
            });

            if (response.ok) {
                this.notificationCount = 0;
                this.updateNotificationBadge();
                this.loadNotificationsList();
            }
        } catch (error) {
            console.error('خطأ في وضع جميع الإشعارات كمقروءة:', error);
        }
    }
}

// Initialize notification system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminNotificationSystem = new AdminNotificationSystem();
});

// Global functions for backward compatibility
window.markNotificationAsRead = async function(notificationId) {
    try {
        const response = await fetch(`/admin/notifications/${notificationId}/read`, {
            method: 'POST'
        });
        
        if (response.ok) {
            window.adminNotificationSystem.loadNotificationsList();
        }
    } catch (error) {
        console.error('خطأ في وضع الإشعار كمقروء:', error);
    }
};
