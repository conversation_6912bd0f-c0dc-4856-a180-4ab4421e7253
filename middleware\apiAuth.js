const jwt = require('jsonwebtoken');
const { Customer, Admin,DeliveryPerson } = require('../models');

/**
 * Middleware للتحقق من صحة JWT token للعملاء
 */
const authenticateCustomer = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        console.log(authHeader);
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('رمز المصادقة مطلوب');
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة مطلوب',
                data: null
            });
        }

        const token = authHeader.substring(7); // إزالة "Bearer "

        // التحقق من صحة التوكن
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        
        // التحقق من نوع المستخدم
        if (decoded.type !== 'customer') {
            return res.status(403).json({
                success: false,
                message: 'غير مصرح لك بالوصول',
                data: null
            });
        }

        // البحث عن العميل
        const customer = await Customer.findByPk(decoded.customerId, {
            attributes: { exclude: ['password'] }
        });

        if (!customer) {
            return res.status(401).json({
                success: false,
                message: 'العميل غير موجود',
                data: null
            });
        }

        // إضافة بيانات العميل للطلب
        req.customer = customer;
        req.token = token;
        
        next();

    } catch (error) {
        console.error('خطأ في مصادقة العميل:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة غير صحيح',
                data: null
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'انتهت صلاحية رمز المصادقة',
                data: null
            });
        }

        return res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
};

/**
 * Middleware للتحقق من صحة JWT token للمتاجر
 */
const authenticateDriver = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            console.log('رمز المصادقة مطلوب');
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة مطلوب',
                data: null
            });
        }

        const token = authHeader.substring(7); // إزالة "Bearer "

        // التحقق من صحة التوكن
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

        // التحقق من نوع المستخدم
        if (decoded.type !== 'driver') {
            return res.status(403).json({
                success: false,
                message: 'غير مصرح لك بالوصول',
                data: null
            });
        }

        // البحث عن العميل
        const driver = await DeliveryPerson.findByPk(decoded.driverId, {
            attributes: { exclude: ['password'] }
        });

        if (!driver) {
            return res.status(401).json({
                success: false,
                message: 'العميل غير موجود',
                data: null
            });
        }

        // إضافة بيانات العميل للطلب
        req.driver = driver;
        req.token = token;
        
        next();

    } catch (error) {
        console.error('خطأ في مصادقة العميل:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة غير صحيح',
                data: null
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'انتهت صلاحية رمز المصادقة',
                data: null
            });
        }

        return res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
};

/**
 * Middleware للتحقق من صحة JWT token للإدارة
 */
const authenticateAdmin = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة مطلوب',
                data: null
            });
        }

        const token = authHeader.substring(7);

        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

        if (decoded.type !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'غير مصرح لك بالوصول',
                data: null
            });
        }

        const admin = await Admin.findByPk(decoded.adminId, {
            attributes: { exclude: ['password'] }
        });

        if (!admin) {
            return res.status(401).json({
                success: false,
                message: 'المدير غير موجود',
                data: null
            });
        }

        req.admin = admin;
        req.token = token;
        // بعد تسجيل الدخول بنجاح:
        messaging.getToken({ vapidKey: "BBH3EJRa-TWHRVwbD1dmtBbmdfzf3SMi-NA8Zey9jW5LM-m5o5mTNUqTkiKMu6Iwn9dvrhEA69f0ZdL_gPuoJVc"}).then((currentToken) => {
        if (currentToken) {
            fetch('/admin/save-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ token: currentToken })
            });
        }
        });

        
        next();

    } catch (error) {
        console.error('خطأ في مصادقة المدير:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'رمز المصادقة غير صحيح',
                data: null
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'انتهت صلاحية رمز المصادقة',
                data: null
            });
        }

        return res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
};

/**
 * Middleware اختياري للمصادقة (يسمح بالوصول بدون تسجيل دخول)
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            // لا يوجد توكن، المتابعة بدون مصادقة
            req.customer = null;
            req.admin = null;
            return next();
        }

        const token = authHeader.substring(7);
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

        // تحديد نوع المستخدم وجلب البيانات
        if (decoded.type === 'customer') {
            const customer = await Customer.findByPk(decoded.customerId, {
                attributes: { exclude: ['password'] }
            });
            req.customer = customer;
        } else if (decoded.type === 'admin') {
            const admin = await Admin.findByPk(decoded.adminId, {
                attributes: { exclude: ['password'] }
            });
            req.admin = admin;
        }

        req.token = token;
        next();

    } catch (error) {
        // في حالة خطأ في التوكن، المتابعة بدون مصادقة
        req.customer = null;
        req.admin = null;
        next();
    }
};

/**
 * Middleware للتحقق من معدل الطلبات (Rate Limiting)
 */
const rateLimiter = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
    const requests = new Map();

    return (req, res, next) => {
        const clientIp =
            req.headers['x-forwarded-for']?.split(',')[0] ||
            req.connection.remoteAddress ||
            req.ip;

        const now = Date.now();
        const windowStart = now - windowMs;

        let clientLogs = requests.get(clientIp) || [];

        // إزالة الطلبات القديمة
        clientLogs = clientLogs.filter(timestamp => timestamp > windowStart);

        if (clientLogs.length >= maxRequests) {
            return res.status(429).json({
                success: false,
                message: 'تم تجاوز الحد الأقصى للطلبات، يرجى المحاولة لاحقاً',
                data: null
            });
        }

        // إضافة الطلب الحالي
        clientLogs.push(now);
        requests.set(clientIp, clientLogs);

        next();
    };
};

// تنظيف الذاكرة: إزالة السجلات القديمة كل 30 دقيقة
setInterval(() => {
    const now = Date.now();
    const expiry = 60 * 60 * 1000; // ساعة واحدة

    for (const [ip, timestamps] of requests) {
        const recent = timestamps.filter(t => now - t < expiry);
        if (recent.length > 0) {
            requests.set(ip, recent);
        } else {
            requests.delete(ip);
        }
    }
}, 30 * 60 * 1000); // كل 30 دقيقة


/**
 * Middleware للتحقق من صحة البيانات
 */
const validateRequest = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body);
        
        if (error) {
            return res.status(400).json({
                success: false,
                message: 'بيانات غير صحيحة',
                data: {
                    errors: error.details.map(detail => ({
                        field: detail.path.join('.'),
                        message: detail.message
                    }))
                }
            });
        }

        next();
    };
};

module.exports = {
    authenticateCustomer,
    authenticateDriver,
    authenticateAdmin,
    optionalAuth,
    rateLimiter,
    validateRequest
};
