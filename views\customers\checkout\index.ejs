<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shopping-cart"></i>
                        مراجعة الطلب
                    </h4>
                </div>
                <div class="card-body">  
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead class="table-light">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <% storeGroup.items.forEach(item => { %>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <% if (item.product.images && item.product.images.length > 0) { %>
                                                            <img src="<%= item.product.images[0].url %>" 
                                                                 alt="<%= item.product.name %>" 
                                                                 class="me-2" 
                                                                 style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                                                        <% } else { %>
                                                            <div class="bg-light me-2 d-flex align-items-center justify-content-center" 
                                                                 style="width: 40px; height: 40px; border-radius: 4px;">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <% } %>
                                                        <div>
                                                            <div class="fw-medium"><%= item.product.name %></div>
                                                            <small class="text-muted"><%= item.product.description || '' %></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><%= item.product.price %> ريال</td>
                                                <td>
                                                    <span class="badge bg-info"><%= item.quantity %></span>
                                                </td>
                                                <td class="fw-bold"><%= item.total %> ريال</td>
                                            </tr>
                                        <% }); %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <% }); %>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- ملخص الطلب -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator"></i>
                        ملخص الطلب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>إجمالي المنتجات:</span>
                        <span class="fw-bold"><%= cartItems.length %></span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <span class="h5">الإجمالي:</span>
                        <span class="h5 text-success fw-bold"><%= totalPrice %> ريال</span>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <small>سيتم إنشاء طلب منفصل لكل متجر</small>
                    </div>
                </div>
            </div>

            <!-- نموذج معلومات التوصيل -->
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-truck"></i>
                        معلومات التوصيل
                    </h5>
                </div>
                <div class="card-body">
                    <form action="/customers/checkout" method="POST" id="checkoutForm">
                        <div class="mb-3">
                            <label for="deliveryAddress" class="form-label">عنوان التوصيل *</label>
                            <textarea class="form-control" 
                                      id="deliveryAddress" 
                                      name="deliveryAddress" 
                                      rows="3" 
                                      required 
                                      placeholder="أدخل عنوان التوصيل بالتفصيل..."><% if (customer.areas && customer.areas.length > 0) { %><%= customer.areas[0].name %><% } %></textarea>
                            <div class="form-text">يرجى إدخال عنوان مفصل لضمان وصول الطلب</div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" 
                                      id="notes" 
                                      name="notes" 
                                      rows="2" 
                                      placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms">
                                    أوافق على <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">الشروط والأحكام</a>
                                </label>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg" id="submitOrder">
                                <i class="fas fa-check-circle"></i>
                                تأكيد الطلب (<%= totalPrice %> ريال)
                            </button>
                            <a href="/customers/cart" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right"></i>
                                العودة للسلة
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للشروط والأحكام -->
<div class="modal fade" id="termsModal" tabindex="-1" aria-labelledby="termsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="termsModalLabel">الشروط والأحكام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6>شروط الطلب والتوصيل:</h6>
                <ul>
                    <li>سيتم إنشاء طلب منفصل لكل متجر</li>
                    <li>كل متجر سيتواصل معك بشكل منفصل</li>
                    <li>أوقات التوصيل قد تختلف حسب المتجر</li>
                    <li>يمكن إلغاء الطلب خلال 30 دقيقة من إنشائه</li>
                    <li>الدفع عند الاستلام</li>
                </ul>
                
                <h6>سياسة الإرجاع:</h6>
                <ul>
                    <li>يمكن إرجاع المنتجات خلال 24 ساعة</li>
                    <li>يجب أن تكون المنتجات في حالتها الأصلية</li>
                    <li>تطبق شروط خاصة على المنتجات الغذائية</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('checkoutForm').addEventListener('submit', function(e) {
    const submitButton = document.getElementById('submitOrder');
    const agreeTerms = document.getElementById('agreeTerms');
    
    if (!agreeTerms.checked) {
        e.preventDefault();
        alert('يرجى الموافقة على الشروط والأحكام');
        return;
    }
    
    // تعطيل الزر لمنع الإرسال المتكرر
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري معالجة الطلب...';
    
    // إعادة تفعيل الزر بعد 10 ثوان في حالة عدم نجاح الإرسال
    setTimeout(() => {
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-check-circle"></i> تأكيد الطلب (<%= totalPrice %> ريال)';
    }, 10000);
});

// تحديث العنوان تلقائياً عند تغيير المنطقة
document.addEventListener('DOMContentLoaded', function() {
    const deliveryAddress = document.getElementById('deliveryAddress');
    if (deliveryAddress.value.trim() === '') {
        deliveryAddress.value = 'يرجى إدخال عنوان التوصيل';
    }
});
</script>

<style>
.store-group {
    border-radius: 8px;
    background-color: #f8f9fa;
    padding: 1rem;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.card-header {
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}
</style>
