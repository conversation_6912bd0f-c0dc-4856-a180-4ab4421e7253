/**
 * نظام إدارة المتاجر الذكي - ملف JavaScript الرئيسي
 * يحتوي على جميع الوظائف التفاعلية للنظام
 */

// متغيرات عامة
let notificationInterval;
let searchTimeout;

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    hideLoadingOverlay();
    initializeNotifications();
    initializeSearch();
    initializeTooltips();
    initializeModals();
    initializeCharts();
    initializeFormValidation();
    initializeKeyboardShortcuts();
    
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoadingOverlay() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        setTimeout(() => {
            loadingOverlay.style.opacity = '0';
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
            }, 300);
        }, 500);
    }
}

/**
 * تهيئة نظام الإشعارات
 */
function initializeNotifications() {
    // تحديث الإشعارات كل 30 ثانية
    updateNotifications();
    notificationInterval = setInterval(updateNotifications, 30000);
    
    // إضافة مستمعي الأحداث للإشعارات
    document.addEventListener('click', function(e) {
        if (e.target.closest('.notification-item')) {
            markNotificationAsRead(e.target.closest('.notification-item'));
        }
    });
}

/**
 * تحديث الإشعارات
 */
async function updateNotifications() {
    try {
        const response = await fetch('/notifications/unread');
        if (response.ok) {
            const data = await response.json();
            updateNotificationBadges(data);
            updateNotificationDropdowns(data);
        }
    } catch (error) {
        console.error('خطأ في تحديث الإشعارات:', error);
    }
}

/**
 * تحديث شارات الإشعارات
 */
function updateNotificationBadges(data) {
    const badges = {
        'customerNotificationCount': data.customer || 0,
        'adminNotificationCount': data.admin || 0
    };
    
    Object.entries(badges).forEach(([id, count]) => {
        const badge = document.getElementById(id);
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }
    });
}

/**
 * تحديث قوائم الإشعارات المنسدلة
 */
function updateNotificationDropdowns(data) {
    const dropdowns = {
        'customerNotificationsList': data.customerNotifications || [],
        'adminNotificationsList': data.adminNotifications || []
    };
    
    Object.entries(dropdowns).forEach(([id, notifications]) => {
        const dropdown = document.getElementById(id);
        if (dropdown) {
            dropdown.innerHTML = renderNotifications(notifications);
        }
    });
}

/**
 * عرض الإشعارات في HTML
 */
function renderNotifications(notifications) {
    if (!notifications || notifications.length === 0) {
        return '<li class="dropdown-item text-center text-muted">لا توجد إشعارات جديدة</li>';
    }
    
    return notifications.map(notification => `
        <li class="dropdown-item notification-item ${notification.isRead ? '' : 'unread'}" 
            data-id="${notification.id}">
            <div class="d-flex align-items-start">
                <div class="me-3">
                    <i class="fas ${getNotificationIcon(notification.type)} text-primary"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${notification.title}</h6>
                    <p class="mb-1 small text-muted">${notification.message}</p>
                    <small class="text-muted">${formatDate(notification.createdAt)}</small>
                </div>
            </div>
        </li>
    `).join('');
}

/**
 * الحصول على أيقونة الإشعار حسب النوع
 */
function getNotificationIcon(type) {
    const icons = {
        'order': 'fa-shopping-cart',
        'payment': 'fa-credit-card',
        'delivery': 'fa-truck',
        'system': 'fa-cog',
        'user': 'fa-user'
    };
    return icons[type] || 'fa-bell';
}

/**
 * تنسيق التاريخ
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'الآن';
    if (diff < 3600000) return `${Math.floor(diff / 60000)} دقيقة`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)} ساعة`;
    
    return date.toLocaleDateString('ar-EG');
}

/**
 * وضع علامة مقروء على الإشعار
 */
async function markNotificationAsRead(notificationElement) {
    const notificationId = notificationElement.dataset.id;
    if (!notificationId) return;
    
    try {
        const response = await fetch(`/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            notificationElement.classList.remove('unread');
            updateNotifications(); // تحديث العدادات
        }
    } catch (error) {
        console.error('خطأ في وضع علامة مقروء:', error);
    }
}

/**
 * وضع علامة مقروء على جميع الإشعارات
 */
async function markAllNotificationsAsRead(type = 'admin') {
    try {
        const response = await fetch(`/notifications/mark-all-read/${type}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            updateNotifications();
            showToast('تم وضع علامة مقروء على جميع الإشعارات', 'success');
        }
    } catch (error) {
        console.error('خطأ في وضع علامة مقروء على جميع الإشعارات:', error);
        showToast('حدث خطأ أثناء العملية', 'error');
    }
}

/**
 * تهيئة البحث المتقدم
 */
function initializeSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performSearch(this);
            }, 500);
        });
    });
    
    // تهيئة الفلاتر
    const filterInputs = document.querySelectorAll('.filter-input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            applyFilters();
        });
    });
}

/**
 * تنفيذ البحث
 */
function performSearch(input) {
    const searchTerm = input.value.trim();
    const form = input.closest('form');
    
    if (form) {
        // تحديث URL بدون إعادة تحميل الصفحة
        const url = new URL(window.location);
        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        
        window.history.pushState({}, '', url);
        
        // إرسال الطلب
        submitForm(form);
    }
}

/**
 * تطبيق الفلاتر
 */
function applyFilters() {
    const form = document.querySelector('.search-filter-form');
    if (form) {
        submitForm(form);
    }
}

/**
 * إرسال النموذج
 */
async function submitForm(form) {
    try {
        showLoading();
        
        const formData = new FormData(form);
        const params = new URLSearchParams(formData);
        
        const response = await fetch(`${form.action}?${params}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (response.ok) {
            const html = await response.text();
            updatePageContent(html);
        }
    } catch (error) {
        console.error('خطأ في إرسال النموذج:', error);
        showToast('حدث خطأ أثناء البحث', 'error');
    } finally {
        hideLoading();
    }
}

/**
 * تحديث محتوى الصفحة
 */
function updatePageContent(html) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // تحديث الجدول
    const newTable = doc.querySelector('.table-container');
    const currentTable = document.querySelector('.table-container');
    
    if (newTable && currentTable) {
        currentTable.innerHTML = newTable.innerHTML;
    }
    
    // تحديث الصفحات
    const newPagination = doc.querySelector('.pagination-container');
    const currentPagination = document.querySelector('.pagination-container');
    
    if (newPagination && currentPagination) {
        currentPagination.innerHTML = newPagination.innerHTML;
    }
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة النوافذ المنبثقة
 */
function initializeModals() {
    // تأكيد الحذف
    document.addEventListener('click', function(e) {
        if (e.target.matches('.delete-btn, .delete-btn *')) {
            e.preventDefault();
            const btn = e.target.closest('.delete-btn');
            showConfirmDialog(
                'تأكيد الحذف',
                'هل أنت متأكد من أنك تريد حذف هذا العنصر؟',
                () => {
                    if (btn.tagName === 'A') {
                        window.location.href = btn.href;
                    } else if (btn.tagName === 'BUTTON') {
                        btn.closest('form').submit();
                    }
                }
            );
        }
    });
}

/**
 * عرض نافذة تأكيد
 */
function showConfirmDialog(title, message, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmBtn">تأكيد</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
    
    modal.querySelector('#confirmBtn').addEventListener('click', () => {
        onConfirm();
        bsModal.hide();
    });
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

/**
 * عرض رسالة توست
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas ${getToastIcon(type)} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    toast.addEventListener('hidden.bs.toast', () => {
        toastContainer.removeChild(toast);
    });
}

/**
 * الحصول على أيقونة التوست
 */
function getToastIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || 'fa-info-circle';
}

/**
 * عرض شاشة التحميل
 */
function showLoading() {
    const loading = document.getElementById('loadingOverlay');
    if (loading) {
        loading.style.display = 'flex';
        loading.style.opacity = '1';
    }
}

/**
 * إخفاء شاشة التحميل
 */
function hideLoading() {
    const loading = document.getElementById('loadingOverlay');
    if (loading) {
        loading.style.opacity = '0';
        setTimeout(() => {
            loading.style.display = 'none';
        }, 300);
    }
}

/**
 * تهيئة الرسوم البيانية
 */
function initializeCharts() {
    // سيتم إضافة الرسوم البيانية هنا لاحقاً
}

/**
 * تهيئة التحقق من النماذج
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
                showToast('يرجى ملء جميع الحقول المطلوبة', 'warning');
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * تهيئة اختصارات لوحة المفاتيح
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveBtn = document.querySelector('.btn-save, .btn-primary[type="submit"]');
            if (saveBtn) saveBtn.click();
        }
        
        // Escape لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.modal.show');
            modals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) bsModal.hide();
            });
        }
        
        // Ctrl + F للبحث
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
    });
}

/**
 * تنظيف الموارد عند مغادرة الصفحة
 */
window.addEventListener('beforeunload', function() {
    if (notificationInterval) {
        clearInterval(notificationInterval);
    }
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
});

/**
 * وظائف مساعدة للتصدير
 */
window.SmartStoreSystem = {
    showToast,
    showConfirmDialog,
    markAllNotificationsAsRead,
    updateNotifications,
    showLoading,
    hideLoading
};
