<div class="container mt-5">
  <h2 class="mb-4">Profile of <%= customer.name %></h2>

  <div class="mb-3">
    <p><strong>Phone:</strong> <%= customer.phoneNumber %></p>
    <p><strong>Status:</strong> 
      <% if (customer.status === 'active') { %>
        <span class="badge bg-success">Active</span>
      <% } else if (customer.status === 'inactive') { %>
        <span class="badge bg-danger">Inactive</span>
      <% } else { %>
        <span class="badge bg-secondary"><%= customer.status %></span>
      <% } %>
    </p>
  </div>

  <hr>

  <h3>Addresses</h3>
  <% if (customer.areas.length === 0) { %>
    <p>No addresses added yet.</p>
  <% } else { %>
    <ul class="list-group mb-4">
      <% customer.areas.forEach(area => { %>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          <div>
            <strong><%= area.name %></strong> — <%= area.CustomerArea.address %>
          </div>
          <form action="/customers/profile/address/<%= area.CustomerArea.area_id %>/delete" method="POST" onsubmit="return confirm('Are you sure you want to delete this address?');">
            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
          </form>
        </li>
      <% }) %>
    </ul>
  <% } %>

  <hr>

  <h3>Add New Address</h3>
  <form action="/customers/profile/address/add" method="POST" class="row g-3 align-items-end">
    <div class="col-md-4">
      <label for="area" class="form-label">Area</label>
      <select name="areaId" id="area" class="form-select" required>
        <% areas.forEach(area => { %>
          <option value="<%= area.id %>"><%= area.name %></option>
        <% }) %>
      </select>
    </div>

    <div class="col-md-6">
      <label for="address" class="form-label">Address</label>
      <input type="text" name="address" id="address" class="form-control" placeholder="Enter your address" required>
    </div>

    <div class="col-md-2">
      <button type="submit" class="btn btn-primary w-100">Add Address</button>
    </div>
  </form>
</div>
