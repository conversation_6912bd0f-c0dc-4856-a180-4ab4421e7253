const { Admin } = require('../models');
const { generateAuthTokens } = require('../utils/jwt');

class AdminAuthController {
    // Show login form
    async showLogin(req, res) {
        res.render('admin/auth/login');
    }

    // Handle login
    async login(req, res) {
        try {
            const { username, password } = req.body;
            const admin = await Admin.findOne({ where: { username } });
            if (!admin) {
                return res.render('admin/auth/login', {
                    error: 'Invalid username or password'
                });
            }

           const isValidPassword = await admin.validatePassword(password);
            if (!isValidPassword) {
                return res.render('admin/auth/login', {
                    error: 'Invalid username or password'
                });
            }

            if (admin.status !== 'active') {
                return res.render('admin/auth/login', {
                    error: 'Your account has been deactivated'
                });
            }

            // Update last login
            admin.lastLogin = new Date();
            await admin.save();

            // إنشاء JWT tokens
            const tokens = generateAuthTokens(admin, 'admin');

            // تعيين الـ token في الـ cookies
            res.cookie('token', tokens.accessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000, // 24 hours
                sameSite: 'strict'
            });

            res.cookie('refreshToken', tokens.refreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                sameSite: 'strict'
            });

            res.redirect('/admin/dashboard');
        } catch (error) {
            res.render('admin/auth/login', {
                error: 'An error occurred during login'
            });
        }
    }

    // Handle logout
    async logout(req, res) {
        // مسح الـ cookies
        res.clearCookie('token');
        res.clearCookie('refreshToken');

        res.redirect('/admin/auth/login?success=' + encodeURIComponent('Logged out successfully'));
    }

    // Change Password
    async showChangePassword(req, res) {
        res.render('admin/auth/change-password');
    }

    async changePassword(req, res) {
        try {
            const { currentPassword, newPassword, confirmPassword } = req.body;
            const admin = await Admin.findByPk(req.user.id);

            if (!admin) {
                return res.render('admin/auth/change-password', {
                    error: 'Admin not found'
                });
            }

            const isValidPassword = await admin.validatePassword(currentPassword);
            if (!isValidPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'Current password is incorrect'
                });
            }

            if (newPassword !== confirmPassword) {
                return res.render('admin/auth/change-password', {
                    error: 'New passwords do not match'
                });
            }

            admin.password = newPassword;
            await admin.save();

            res.render('admin/auth/change-password', {
                success: 'Password changed successfully'
            });
        } catch (error) {
            res.render('admin/auth/change-password', {
                error: 'An error occurred while changing password'
            });
        }
    }
}

module.exports = new AdminAuthController();