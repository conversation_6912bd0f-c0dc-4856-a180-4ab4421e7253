const BaseController = require('./BaseController');
const { Order, Customer, Product, OrderDetail, DeliveryPerson, Delivery, Notification, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');
const NotificationService = require('../services/NotificationService');

class OrdersController extends BaseController {
    constructor() {
        super(Order, 'orders');
    }

    // عرض قائمة الطلبات للإدارة
    async index(req, res) {
        try {
            // إعداد خيارات البحث والفلتر
            const searchFields = {
                text: ['id'],
                numeric: ['id', 'totalPrice']
            };

            const filterFields = {
                status: { type: 'exact' },
                customerId: { type: 'exact' },
                createdAt: { type: 'date' }
            };

            // بناء شروط البحث والفلتر
            const { searchConditions, filterConditions } = buildSearchAndFilter(req.query, searchFields, filterFields);

            // دمج الشروط في كائن واحد
            const whereClause = {
            ...searchConditions,
            ...filterConditions
            };

            // خيارات الترتيب
            const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

            // خيارات الـ pagination
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

            // جلب البيانات
            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            attributes: ['id', 'name', 'price']
                        }]
                    }
                ],
                order: sortOptions,
                limit: paginationOptions.limit,
                offset: paginationOptions.offset,
                distinct: true
            });

            // حساب معلومات الـ pagination
            const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

            // تنظيف الفلاتر للعرض
            const filters = sanitizeFilters(req.query);

            // جلب العملاء للفلتر
            const customers = await Customer.findAll({
                attributes: ['id', 'name'],
                order: [['name', 'ASC']]
            });

            res.render('admin/orders/index', {
                orders,
                pagination,
                filters,
                customers,
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(filters).length
            });
        } catch (error) {
            console.error('Error fetching orders:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch orders' } });
        }
    }

    // عرض الطلبات في حالة الانتظار
    async pendingOrders(req, res) {
        try {
            // إعداد خيارات البحث والفلتر للطلبات المعلقة فقط
            const searchFields = {
                text: ['id'],
                numeric: ['id', 'totalPrice']
            };

            const filterFields = {
                customerId: { type: 'exact' },
                createdAt: { type: 'date' }
            };

            // إضافة فلتر ثابت للحالة pending
            const fixedFilters = {
                status: 'pending'
            };

            const { searchConditions, filterConditions } = buildSearchAndFilter(
                req.query,
                searchFields,
                filterFields,
                fixedFilters
            );

            const sortOptions = buildSortOptions(req.query.sort, req.query.order, 'createdAt', 'DESC');
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit, 20);

            // جلب الطلبات المعلقة مع العلاقات
            const { count, rows: orders } = await Order.findAndCountAll({
                where: {
                    ...searchConditions,
                    ...filterConditions
                },
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber', 'address']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            attributes: ['id', 'name', 'price']
                        }]
                    }
                ],
                order: [sortOptions],
                ...paginationOptions
            });

            // حساب معلومات الصفحات
            const pagination = calculatePaginationInfo(count, paginationOptions.limit, req.query.page);

            // إحصائيات سريعة للطلبات المعلقة
            const stats = {
                totalPending: await Order.count({
                    where: {
                        status: 'pending'
                    }
                }),
                todayPending: await Order.count({
                    where: {
                        status: 'pending',
                        createdAt: {
                            [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
                        }
                    }
                }),
                totalValue: orders.reduce((sum, order) => sum + parseFloat(order.totalPrice || 0), 0)
            };
            res.render('admin/orders/pending', {
                orders,
                pagination,
                stats,
                filters: sanitizeFilters(req.query),
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(req.query).filter(key =>
                    !['page', 'limit', 'sort', 'order'].includes(key) && req.query[key]
                ).length,
                title: 'الطلبات في حالة الانتظار'
            });
        } catch (error) {
            console.error('Error fetching pending orders:', error);
            res.status(500).render('error', {
                error: {
                    message: 'حدث خطأ أثناء جلب الطلبات المعلقة',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async create(req, res) {
        try {
            const [customers, products] = await Promise.all([
                executeWithRetry(async () => Customer.findAll({ where: { status: 'active' } }),
                    { useTransaction: false }),
                executeWithRetry(async () => Product.findAll({
                    where: {
                        status: 'active',
                        quantity: { [sequelize.Op.gt]: 0 }
                    }}),
                    { useTransaction: false })
            ]);

            res.render('orders/create', { customers, products });
        } catch (error) {
            console.error('Error in OrdersController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async insert(req, res) {
        try {
            let createdOrder = null;

            await executeWithRetry(async (transaction) => {
                const { orderDetails, ...orderData } = req.body;
                const order = await Order.create(orderData, { transaction });
                createdOrder = order;

                if (Array.isArray(orderDetails)) {
                    // Calculate totalPrice for each order detail
                    const detailsWithTotalPrice = await Promise.all(
                        orderDetails.map(async (detail) => {
                            const product = await Product.findByPk(detail.productId, { transaction });
                            if (!product) {
                                throw new Error(`Product with ID ${detail.productId} not found`);
                            }
                            return {
                                ...detail,
                                orderId: order.id,
                                totalPrice: product.price * detail.quantity
                            };
                        })
                    );
                    await OrderDetail.bulkCreate(detailsWithTotalPrice, { transaction });
                }
            });

            // إرسال إشعار للمتجر عن الطلب الجديد
            if (createdOrder) {
                try {
                    await this.sendNewOrderNotifications(createdOrder);
                } catch (notificationError) {
                    logger.error('Error sending order notifications:', notificationError);
                    // لا نوقف العملية إذا فشل الإشعار
                }
            }

            req.flash('success', 'Order created successfully');
            res.redirect('/orders');
        } catch (error) {
            console.error('Error in OrdersController:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while creating the order',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

  

    async show(req, res) {
    try {
        const order = await Order.findByPk(req.params.id, {
        include: [
            {
            model: Customer,
            as: 'customer',
            attributes: ['id', 'name', 'phoneNumber', 'address']
            },
            {
            model: OrderDetail,
            as: 'orderDetails',
            include: [{
                model: Product,
                as: 'product',
                attributes: ['id', 'name', 'price']
            }]
            }
        ]
        });

        if (!order) {
        return res.status(404).render('error', {
            error: { message: 'Order not found' }
        });
        }

        res.render('admin/orders/show', {
        order,
        deliveryPeople: [],
        title: `تفاصيل الطلب #${order.id}`
        });
    } catch (error) {
        console.error('Error in OrdersController.show:', error);
        res.status(500).render('error', {
        error: {
            message: 'An error occurred while fetching the order',
            details: process.env.NODE_ENV === 'development' ? error.message : null
        }
        });
  }
}


    // الموافقة على طلب
    async approve(req, res) {
        try {
            const order = await Order.findByPk(req.params.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    }
                ]
            });

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'pending') {
                return res.status(400).json({ success: false, message: 'لا يمكن الموافقة على هذا الطلب' });
            }

            // تحديث حالة الطلب
            await order.update({ status: 'processing' });

            // إرسال إشعار للعميل
            await NotificationService.notifyCustomer(order.customer.id, {
                title: 'تم الموافقة على طلبك',
                message: `تم الموافقة على طلبك رقم #${order.id} وسيتم تحضيره قريباً`,
                type: 'success',
                priority: 'high',
                actionUrl: `/customers/orders/${order.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: order.id,
                    type: 'order_approved'
                }
            });

            res.json({ success: true, message: 'تم الموافقة على الطلب وإرسال الإشعارات بنجاح' });
        } catch (error) {
            console.error('Error approving order:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء الموافقة على الطلب' });
        }
    }

    // رفض طلب
    async reject(req, res) {
        try {
            const order = await Order.findByPk(req.params.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    }
                ]
            });

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'pending') {
                return res.status(400).json({ success: false, message: 'لا يمكن رفض هذا الطلب' });
            }

            const { reason } = req.body;

            // تحديث حالة الطلب
            await order.update({
                status: 'rejected',
                notes: reason || 'لم يتم تحديد السبب'
            });

            // إرسال إشعار للعميل
            await NotificationService.notifyCustomer(order.customer.id, {
                title: 'تم رفض طلبك',
                message: `تم رفض طلبك رقم #${order.id}${reason ? ` - السبب: ${reason}` : ''}`,
                type: 'error',
                priority: 'high',
                actionUrl: `/customers/orders/${order.id}`,
                actionText: 'عرض التفاصيل',
                data: {
                    orderId: order.id,
                    type: 'order_rejected',
                    reason: reason
                }
            });

            res.json({ success: true, message: 'تم رفض الطلب وإرسال الإشعار بنجاح' });
        } catch (error) {
            console.error('Error rejecting order:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء رفض الطلب' });
        }
    }

    // تسليم طلب لمندوب توصيل
    async assignDelivery(req, res) {
        try {
            const order = await Order.findByPk(req.params.id);

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'processing') {
                return res.status(400).json({ success: false, message: 'يجب الموافقة على الطلب أولاً' });
            }

            const { deliveryPersonId } = req.body;

            // التحقق من وجود مندوب التوصيل
            const deliveryPerson = await DeliveryPerson.findByPk(deliveryPersonId);
            if (!deliveryPerson) {
                return res.status(404).json({ success: false, message: 'مندوب التوصيل غير موجود' });
            }

            // إنشاء مهمة توصيل
            const delivery = await Delivery.create({
                orderId: order.id,
                deliveryPersonId: deliveryPersonId,
                status: 'assigned',
                assignedAt: new Date()
            });

            // تحديث حالة الطلب
            await order.update({ status: 'out_for_delivery' });

            // إرسال إشعار للعميل
            await Notification.create({
                customerId: order.customerId,
                title: 'طلبك في الطريق',
                message: `طلبك رقم #${order.id} في الطريق إليك مع مندوب التوصيل ${deliveryPerson.name}`,
                type: 'order_out_for_delivery',
                relatedId: order.id
            });

            res.json({
                success: true,
                message: 'تم تسليم الطلب لمندوب التوصيل بنجاح',
                delivery: {
                    id: delivery.id,
                    deliveryPerson: {
                        name: deliveryPerson.name,
                        phoneNumber: deliveryPerson.phoneNumber
                    }
                }
            });
        } catch (error) {
            console.error('Error assigning delivery:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء تسليم الطلب' });
        }
    }

    // تحديث حالة طلب
    async updateStatus(req, res) {
        try {
            const order = await Order.findByPk(req.params.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    }
                ]
            });

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            const { status, notes } = req.body;
            const validStatuses = ['pending', 'processing', 'rejected', 'out_for_delivery', 'completed', 'cancelled'];

            if (!validStatuses.includes(status)) {
                return res.status(400).json({ success: false, message: 'حالة غير صحيحة' });
            }

            const oldStatus = order.status;

            // تحديث حالة الطلب
            await order.update({
                status,
                notes: notes || order.notes
            });

            // إرسال إشعار للعميل عن طريق NotificationService
            const statusMessages = {
                'pending': 'تم تعليق طلبك',
                'approved': 'تمت الموافقة على طلبك وسيتم تحضيره قريباً',
                'rejected': 'تم رفض طلبك' + (notes ? ` - السبب: ${notes}` : ''),
                'preparing': 'جاري تحضير طلبك',
                'out_for_delivery': 'طلبك في الطريق إليك',
                'delivered': 'تم تسليم طلبك بنجاح',
                'cancelled': 'تم إلغاء طلبك' + (notes ? ` - السبب: ${notes}` : '')
            };

            const statusTypes = {
                'pending': 'info',
                'approved': 'success',
                'rejected': 'error',
                'preparing': 'info',
                'out_for_delivery': 'info',
                'delivered': 'success',
                'cancelled': 'warning'
            };

            const statusPriorities = {
                'pending': 'normal',
                'approved': 'high',
                'rejected': 'high',
                'preparing': 'normal',
                'out_for_delivery': 'high',
                'delivered': 'normal',
                'cancelled': 'high'
            };

            await NotificationService.notifyCustomer(order.customer.id, {
                title: 'تحديث حالة الطلب',
                message: `${statusMessages[status]} - طلب رقم #${order.id}`,
                type: statusTypes[status],
                priority: statusPriorities[status],
                actionUrl: `/customers/orders/${order.id}`,
                actionText: 'عرض تفاصيل الطلب',
                data: {
                    orderId: order.id,
                    status: status,
                    type: 'order_status_update',
                    notes: notes
                }
            });

            // إرسال إشعار للمدراء عن تحديث حالة الطلب
            if (oldStatus !== status) {
                try {
                    const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                    const statusTextAr = {
                        'pending': 'في الانتظار',
                        'processing': 'قيد المعالجة',
                        'rejected': 'مرفوض',
                        'out_for_delivery': 'في الطريق',
                        'completed': 'مكتمل',
                        'cancelled': 'ملغي'
                    };

                    const notification = {
                        title: 'تحديث حالة طلب',
                        body: `تم تغيير حالة الطلب #${order.id} من ${statusTextAr[oldStatus]} إلى ${statusTextAr[status]}`
                    };

                    const data = {
                        orderId: order.id.toString(),
                        customerId: order.customer.id.toString(),
                        customerName: order.customer.name,
                        oldStatus: oldStatus,
                        newStatus: status,
                        clickAction: `/admin/orders/${order.id}`,
                        type: 'order_status_update_admin'
                    };

                    const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
                    console.log(`📊 Order status notification results: ${result.successCount} successful, ${result.failureCount} failed`);
                } catch (notificationError) {
                    console.error('❌ Error sending order status notification:', notificationError);
                }
            }


            res.json({ 
                success: true, 
                message: 'تم تحديث حالة الطلب وإرسال الإشعارات بنجاح',
                newStatus: status
            });
        } catch (error) {
            console.error('Error updating order status:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء تحديث الحالة' });
        }
    }
}

module.exports = new OrdersController();
