const { DeliveryPerson, Delivery,  DeliveryPeopleArea } = require('../models');
const path = require('path');
const bcrypt = require('bcryptjs');
const deliverypersonarea = require('../models/deliverypersonarea');
const fs = require('fs');
// عرض جميع السائقين  
exports.index = async (req, res) => {
  const limit = 20;
  const currentPage = parseInt(req.query.page) || 1;
  const offset = (currentPage - 1) * limit;

  try {
    const { count, rows: deliveryPeople } = await DeliveryPerson.findAndCountAll({
      include: [{ model: Delivery, as: 'deliveries' }],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/drivers/index', {
      deliveryPeople,
      currentPage,
      totalPages
    });
  } catch (error) {
    console.error("Error fetching delivery people:", error);
    res.status(500).render('error', {
      error: { message: 'Unable to fetch delivery people' }
    });
  }
};

// عرض صفحة إضافة سائق
exports.createForm = (req, res) => {
  res.render('admin/drivers/create');
};

// إنشاء سائق جديد
exports.create = async (req, res) => {
  try {
    const { name,username, password, phoneNumber, regions } = req.body;

    // صورة السائق
    let imagePath = null;
    if (req.file) {
      imagePath = `/uploads/deliveries/${req.file.filename}`;
    }
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);
    // إنشاء السائق
    const driver = await DeliveryPerson.create({
      name,
      username,
      password : hashedPassword,
      phoneNumber,
      image: imagePath,
    });

    // إرسال إشعار للمدراء عن السائق الجديد
    try {
      const FirebaseMessagingService = require('../services/FirebaseMessagingService');

      const notification = {
        title: 'سائق توصيل جديد',
        body: `تم إضافة سائق توصيل جديد: ${name} - ${phoneNumber}`
      };

      const data = {
        driverId: driver.id.toString(),
        driverName: name,
        driverPhone: phoneNumber,
        clickAction: `/admin/drivers/${driver.id}`,
        type: 'new_driver'
      };

      const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
      console.log(`📊 Driver notification results: ${result.successCount} successful, ${result.failureCount} failed`);
    } catch (notificationError) {
      console.error('❌ Error sending driver notification:', notificationError);
      // لا نوقف العملية إذا فشل الإشعار
    }

    // تأكد أن المناطق مصفوفة
    const regionArray = Array.isArray(regions) ? regions : Object.values(regions);

    // حفظ المناطق وربطها بالسائق
    for (const regionData of regionArray) {
      await DeliveryPeopleArea.create({
        deliveryPeople_id: driver.id,
        city: regionData.city,
        region: regionData.region,
        address: regionData.address,
        notes: regionData.notes || null
      });
    }

    req.flash('success', 'تمت إضافة السائق بنجاح');
    res.redirect('/admin/drivers');
  } catch (error) {
    console.error('فشل في إضافة السائق:', error);
    req.flash('error', 'حدث خطأ أثناء إضافة السائق');
    res.redirect('back');
  }
};

// عرض صفحة تعديل سائق
exports.editForm = async (req, res) => {
const deliveryPerson = await DeliveryPerson.findByPk(req.params.id, {
  include: [
    {
      model: DeliveryPeopleArea,
      as: 'areas'
    }
  ]
});
res.render('admin/drivers/edit', { deliveryPerson });
};

exports.update = async (req, res) => {
  try {
    const driverId = req.params.id;
    const { name,username,password, phoneNumber, status } = req.body;

    const driver = await DeliveryPerson.findByPk(driverId);
    if (!driver) return res.status(404).send('السائق غير موجود');

    // تحديث البيانات الأساسية
    driver.name = name;
    driver.username = username;
     // تحديث كلمة المرور إذا تم إدخالها
    if (password && password.trim() !== '') {
        driver.password = await bcrypt.hash(password, 10);
    }
    driver.phoneNumber = phoneNumber;
    driver.status = status;

    // إذا كانت هناك صورة جديدة مرفوعة
    if (req.file) {
      // حذف الصورة القديمة إذا كانت موجودة
      if (driver.image) {
        const oldPath = path.join(__dirname, '..', 'public', driver.image);
        if (fs.existsSync(oldPath)) {
          fs.unlinkSync(oldPath);
        }
      }

      // حفظ اسم الصورة الجديدة
      driver.image = `/uploads/deliveries/${req.file.filename}`;
    }

    await driver.save();

    // حذف جميع المناطق القديمة وإضافة الجديدة
    await DeliveryPeopleArea.destroy({ where: { deliveryPeople_id: driver.id } });

    const regions = req.body.regions || {};
    const regionsArray = Object.values(regions);

    for (const region of regionsArray) {
      await DeliveryPeopleArea.create({
        deliveryPeople_id: driver.id,
        city: region.city,
        region: region.region,
        address: region.address,
        latitude: region.latitude,
        longitude: region.longitude
      });
    }

    res.redirect('/admin/drivers'); // أو أي مسار رجوع
  } catch (err) {
    console.error(err);
    res.status(500).send('حدث خطأ أثناء التحديث');
  }
};

exports.delete = async (req, res) => {
  const { id } = req.params;

  try {
    await DeliveryPerson.destroy({ where: { id } });
    res.redirect('/admin/drivers');
  } catch (error) {
    console.error('خطأ أثناء حذف السائق:', error);
    res.status(500).send('حدث خطأ أثناء حذف السائق');
  }
};