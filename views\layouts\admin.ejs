<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - نظام إدارة المتاجر الذكي</title>

    <!-- Meta Tags -->
    <meta name="description" content="لوحة إدارة نظام المتاجر الذكي">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="/css/fixed-main.css" rel="stylesheet">
    <link href="/css/admin.css" rel="stylesheet">
    
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Sidebar Toggle Button (Mobile) -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-bars"></i>
    </button>

    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="admin-sidebar-header">
                <a href="/admin/dashboard" class="admin-logo">
                    <i class="fas fa-shield-alt"></i>
                    لوحة الإدارة
                </a>
            </div>

            <nav class="admin-nav">
                <!-- Dashboard Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">الرئيسية</div>
                    <div class="admin-nav-item">
                        <a href="/admin/dashboard" class="admin-nav-link <%= locals.currentPage === 'dashboard' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-tachometer-alt"></i>
                            <span class="admin-nav-text">لوحة التحكم</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/analytics" class="admin-nav-link <%= locals.currentPage === 'analytics' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-chart-line"></i>
                            <span class="admin-nav-text">التحليلات</span>
                        </a>
                    </div>
                </div>

                <!-- Management Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">الإدارة</div>
                    <div class="admin-nav-item">
                        <a href="/admin/customers" class="admin-nav-link <%= locals.currentPage === 'customers' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-users"></i>
                            <span class="admin-nav-text">العملاء</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/orders" class="admin-nav-link <%= locals.currentPage === 'orders' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-shopping-bag"></i>
                            <span class="admin-nav-text">الطلبات</span>
                            <span class="admin-nav-badge" id="newOrders">0</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/products" class="admin-nav-link <%= locals.currentPage === 'products' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-box"></i>
                            <span class="admin-nav-text">المنتجات</span>
                        </a>
                    </div>
                </div>

                <!-- Content Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">المحتوى</div>
                    <div class="admin-nav-item">
                        <a href="/admin/categories" class="admin-nav-link <%= locals.currentPage === 'categories' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-tags"></i>
                            <span class="admin-nav-text">الفئات</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/areas" class="admin-nav-link <%= locals.currentPage === 'areas' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-map-marker-alt"></i>
                            <span class="admin-nav-text">المناطق</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/notifications" class="admin-nav-link <%= locals.currentPage === 'notifications' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-bell"></i>
                            <span class="admin-nav-text">الإشعارات</span>
                            <span class="admin-nav-badge" id="unreadNotifications">0</span>
                        </a>
                    </div>
                </div>

                <!-- System Section -->
                <div class="admin-nav-section">
                    <div class="admin-nav-title">النظام</div>
                    <div class="admin-nav-item">
                        <a href="/admin/settings" class="admin-nav-link <%= locals.currentPage === 'settings' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-cog"></i>
                            <span class="admin-nav-text">الإعدادات</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/users" class="admin-nav-link <%= locals.currentPage === 'users' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-user-shield"></i>
                            <span class="admin-nav-text">المديرين</span>
                        </a>
                    </div>
                    <div class="admin-nav-item">
                        <a href="/admin/logs" class="admin-nav-link <%= locals.currentPage === 'logs' ? 'active' : '' %>">
                            <i class="admin-nav-icon fas fa-file-alt"></i>
                            <span class="admin-nav-text">السجلات</span>
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <div class="admin-header-content">
                    <div>
                        <h1 class="admin-header-title">
                            <% if (locals.pageTitle) { %>
                                <%= pageTitle %>
                            <% } else { %>
                                لوحة التحكم
                            <% } %>
                        </h1>
                    </div>
                    
                    <div class="admin-header-actions">
                        <!-- Search -->
                        <div class="search-box d-none d-md-block">
                            <input type="text" class="form-control" placeholder="البحث..." id="globalSearch">
                            <i class="fas fa-search"></i>
                        </div>

                        <!-- Notifications -->
                        <div class="dropdown">
                            <button class="btn btn-secondary position-relative" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge" id="headerNotificationCount" style="display: none;">0</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">الإشعارات الجديدة</li>
                                <div id="headerNotificationsList"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="/notifications">عرض جميع الإشعارات</a></li>
                            </ul>
                        </div>

                        <!-- User Menu -->
                        <div class="dropdown">
                            <a href="#" class="admin-user-menu" data-bs-toggle="dropdown">
                                <div class="admin-user-avatar">
                                    <% if (locals.session && locals.session.adminName) { %>
                                        <%= session.adminName.charAt(0).toUpperCase() %>
                                    <% } else { %>
                                        A
                                    <% } %>
                                </div>
                                <div class="d-none d-md-block">
                                    <div style="font-weight: 600;">
                                        <% if (locals.session && locals.session.adminName) { %>
                                            <%= session.adminName %>
                                        <% } else { %>
                                            المدير
                                        <% } %>
                                    </div>
                                    <div style="font-size: 0.75rem; color: var(--text-muted);">مدير النظام</div>
                                </div>
                                <i class="fas fa-chevron-down d-none d-md-block"></i>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="/admin/profile">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="/admin/auth/change-password">تغيير كلمة المرور</a></li>
                                <li><a class="dropdown-item" href="/admin/settings">الإعدادات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Flash Messages -->
            <div class="admin-main">
                <% if (locals.success && success.length > 0) { %>
                    <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInDown">
                        <i class="fas fa-check-circle me-2"></i>
                        <%= success %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>
                <% if (locals.error && error.length > 0) { %>
                    <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInDown">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <%= error %>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <% } %>

                <!-- Page Content -->
                <%- body %>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/js/admin.js"></script>
    
    <!-- Page-specific scripts -->
    <% if (locals.pageScripts) { %>
        <%- pageScripts %>
    <% } %>

    <script>
        // Sidebar toggle functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            document.getElementById('adminSidebar').classList.toggle('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('adminSidebar');
            const toggle = document.getElementById('sidebarToggle');
            
            if (window.innerWidth <= 1024 && 
                !sidebar.contains(event.target) && 
                !toggle.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('show')) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 300);
                }
            });
        }, 5000);
    </script>
</body>
</html>
