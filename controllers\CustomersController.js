const BaseController = require('./BaseController');
const { Customer, Order, Category, Product, Image, OrderDetail, Notification,Delivery,DeliveryPerson, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const { custom } = require('joi');
class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }

     async getHome(req, res) {
    try {
        const customerId = req.customer?.id;

        // المنتجات التي تحتوي على صورة (offers)
        const offerProducts = await Product.findAll({
        where: {
            image: {
            [Op.and]: [
                { [Op.ne]: null },
                { [Op.ne]: '' }
            ]
            },
            status: 'active'
        },
        attributes: ['id', 'image']
        });

        // المنتجات الأعلى تقييماً
        const topProducts = await Product.findAll({
        where: {
            rating: {
            [Op.not]: null
            },
            status: 'active'
        },
        include: [
            {
            model: Image,
            as: 'images',
            attributes: ['image'],
            required: false
            }
        ],
        attributes: ['id', 'name', 'description', 'price', 'quantity', 'rating'],
        order: [['rating', 'DESC']],
        limit: 10
        });

        // الاستجابة
        res.json({
        success: true,
        message: 'تم جلب البيانات بنجاح',
        data: {
            offerProducts,
            topProducts
        }
        });

    } catch (error) {
        console.error('خطأ في جلب الصفحة الرئيسية:', error);
        res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
        });
    }
    }

    // عرض قائمة العملاء للإدارة
    async index(req, res) {
        try {
            // إعداد خيارات البحث والفلتر
            const searchFields = {
                text: ['name', 'barcode', 'phoneNumber'],
                numeric: ['id']
            };

            const filterFields = {
                status: { type: 'exact' },
                createdAt: { type: 'date' }
            };
            const { searchConditions, filterConditions } = buildSearchAndFilter(
                req.query,
                searchFields,
                filterFields
            );

            // دمج الشرطين مع التحقق من عدم تمرير {} فارغ
            const whereClause = {
                ...(Object.keys(searchConditions).length > 0 ? { [Op.and]: [searchConditions] } : {}),
                ...(Object.keys(filterConditions).length > 0 ? filterConditions : {})
            };

            // خيارات الترتيب
            const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

            // خيارات الـ pagination
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

            // جلب البيانات
            const { count, rows: customers } = await Customer.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Order, as: 'orders' }
                ],
                order: sortOptions,
                limit: paginationOptions.limit,
                offset: paginationOptions.offset,
                distinct: true
            });

            // حساب معلومات الـ pagination
            const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

            // تنظيف الفلاتر للعرض
            const filters = sanitizeFilters(req.query);

            res.render('admin/customers/index', {
                customers,
                pagination,
                filters,
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(filters).length
            });
        } catch (error) {
            console.error('Error fetching customers:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch customers' } });
        }
    }

    // عرض تفاصيل عميل
    async show(req, res) {
        const customerId = req.params.id;

        try {
            const customer = await Customer.findByPk(customerId, {
                include: [
                    { model: Order, as: 'orders' }
                ]
            });

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/show', { customer });
        } catch (error) {
            console.error("Error fetching customer:", error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات العميل' } });
        }
    }


    async getProductsByCategory(req, res) {
        try {
            const categoryId = req.params.id;
            const { page = 1, limit = 10, search = '', area = null } = req.query;

            const category = await Category.findByPk(categoryId);
            if (!category) {
                return res.status(404).json({
                    success: false,
                    message: 'الفئة غير موجودة',
                    data: null
                });
            }

            const offset = (page - 1) * limit;
            const whereClause = { status: "active",
                categoryId: categoryId
             };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }


            // تنفيذ الاستعلام
            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب المتاجر بنجاح',
                data: {
                   /* category: {
                        id: category.id,
                        name: category.name,
                        description: category.description
                    },*/
                    products,
                    categoryname : category.name
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

     async getProducts(req, res) {
        try {
            const { page = 1, limit = 10, search = '', area = null } = req.query;



           // const offset = (page - 1) * limit;
            const whereClause = { status: "active"
             };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }


            // تنفيذ الاستعلام
            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
//                limit: parseInt(limit),
//                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب المتاجر بنجاح',
                data: {
                    products,
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // عرض نموذج إضافة عميل جديد
    async create(req, res) {
        try {
            res.render('admin/customers/create', {
                title: 'إضافة عميل جديد'
            });
        } catch (error) {
            console.error('Error loading create customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // إنشاء عميل جديد
    async create(req, res) {
      try {
       const { name, phoneNumber, password, barcode, discountRate, notes, status, address, latitude, longitude, city, region, permissions } = req.body;
    
         // التحقق من البيانات المطلوبة
            if (!name || !phoneNumber || !password) {
                return res.status(400).render('admin/customers/create', {
                    error: 'الاسم ورقم الهاتف وكلمة المرور مطلوبة',
                    formData: req.body
                });
            }

            // التحقق من عدم وجود عميل بنفس رقم الهاتف
            const existingCustomer = await Customer.findOne({ where: { phoneNumber } });
            if (existingCustomer) {
                return res.status(400).render('admin/customers/create', {
                    error: 'يوجد عميل مسجل بهذا رقم الهاتف',
                    formData: req.body
                });
            }
        
        // تشفير كلمة المرور
          // معالجة رفع الصورة
            let imagePath = null;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // معالجة الصلاحيات (0 أو 1)
            const customerPermissions = parseInt(permissions) || 0;
        // إنشاء العميل
            await Customer.create({
                name,
                phoneNumber,
                password: Password,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                city: city || null,
                region: region || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status: status || 'active'
            });
    
            req.flash('success', 'تم إضافة العميل بنجاح');
            res.redirect('/admin/customers');
      } catch (error) {
            console.error('Error creating customer:', error);
            res.status(500).render('admin/customers/create', {
                error: 'حدث خطأ أثناء إضافة العميل',
                formData: req.body
            });
        }
    };
    // عرض نموذج تعديل عميل
    async edit(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/edit', {
                customer,
                title: `تعديل العميل: ${customer.name}`
            });
        } catch (error) {
            console.error('Error loading edit customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // تحديث بيانات عميل
    async update(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { name, phoneNumber, barcode, discountRate, notes, status, password, address, latitude, longitude, city, region, permissions } = req.body;

            // التحقق من عدم وجود عميل آخر بنفس رقم الهاتف
            if (phoneNumber !== customer.phoneNumber) {
                const existingCustomer = await Customer.findOne({
                    where: {
                        phoneNumber,
                        id: { [Op.ne]: customer.id }
                    }
                });

                if (existingCustomer) {
                    return res.status(400).render('admin/customers/edit', {
                        error: 'يوجد عميل آخر مسجل بهذا رقم الهاتف',
                        customer: { ...customer.toJSON(), ...req.body }
                    });
                }
            }

            // معالجة رفع الصورة الجديدة
            let imagePath = customer.image;
            if (req.file) {
                imagePath = `/uploads/customers/${req.file.filename}`;
            }

            // معالجة الصلاحيات (0 أو 1)
            const customerPermissions = parseInt(permissions) || 0;

            // إعداد البيانات للتحديث
            const updateData = {
                name,
                phoneNumber,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                city: city || null,
                region: region || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status
            };

            // تحديث كلمة المرور إذا تم إدخالها
            if (password && password.trim() !== '') {
                updateData.password = password;
            }

            // تحديث البيانات
            await customer.update(updateData);

            req.flash('success', 'تم تحديث بيانات العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer:', error);
            res.status(500).render('admin/customers/edit', {
                error: 'حدث خطأ أثناء تحديث البيانات',
                customer: { ...req.body, id: req.params.id }
            });
        }
    }

    // حذف عميل
    async delete(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            // حذف ناعم - تغيير الحالة إلى inactive
            await customer.update({ status: 'inactive' });

            req.flash('success', 'تم حذف العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error deleting customer:', error);
            req.flash('error', 'حدث خطأ أثناء حذف العميل');
            res.redirect('/admin/customers');
        }
    }

    // تحديث حالة عميل
    async updateStatus(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { status } = req.body;

            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'حالة غير صحيحة');
                return res.redirect('/admin/customers');
            }

            await customer.update({ status });

            req.flash('success', `تم تحديث حالة العميل إلى ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            req.flash('error', 'حدث خطأ أثناء تحديث الحالة');
            res.redirect('/admin/customers');
        }
    }

    // API: الحصول على الفئات
    async getCategory(req, res) {
        try {
            const categories = await Category.findAll({
                attributes: ['id', 'name', 'image'],
                order: [['name', 'ASC']]
            });
            
            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: categories
            });

        } catch (error) {
            console.error('خطأ في جلب الفئات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: الحصول على تفاصيل منتج
    async getProductDetails(req, res) {
        try {
            const productId = req.params.id;

            const product = await Product.findOne({
                where: {
                    id: productId,
                    status: "active"
                },
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب تفاصيل المنتج بنجاح',
                data: product
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }


    async getCheckoutData(req, res) {
        try {
            const customerId = req.customer.id;

            // جلب محتويات السلة باستخدام CartService
            const cartData = await CartService.getCart(customerId);

            if (!cartData.cartItems || cartData.cartItems.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'سلة التسوق فارغة',
                    data: null
                });
            }

            // جلب بيانات العميل
            const customer = await Customer.findByPk(customerId, {
                include: [
                    {
                        model: Area,
                        as: 'areas',
                        include: [
                            {
                                model: Country,
                                as: 'country',
                                attributes: ['id', 'name']
                            }
                        ]
                    }
                ],
                attributes: { exclude: ['password'] }
            });

            // تنسيق بيانات الدفع
            const checkoutData = {
                customer: {
                    id: customer.id,
                    name: customer.name,
                    email: customer.email,
                    phone: customer.phone,
                    address: customer.address,
                    areas: customer.areas ? customer.areas.map(area => ({
                        id: area.id,
                        name: area.name,
                        address: area.CustomerArea ? area.CustomerArea.address : null,
                        country: area.Country ? area.Country.name : null
                    })) : []
                },
                cart: {
                    items: cartData.cartItems.map(item => ({
                        id: item.id,
                        quantity: item.quantity,
                        price: parseFloat(item.price),
                        totalPrice: parseFloat(item.totalPrice),
                        notes: item.notes,
                        product: {
                            id: item.product.id,
                            name: item.product.name,
                            description: item.product.description,
                            availableQuantity: item.product.quantity
                        }
                    })),
                   
                    summary: cartData.summary
                }
            };

            res.json({
                success: true,
                message: 'تم جلب بيانات الدفع بنجاح',
                data: checkoutData
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات الدفع:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }


    async processCheckout(req, res) {
        const transaction = await sequelize.transaction();

        try {
            const customerId = req.customer.id;
            console.log(req.body);
            const { deliveryAddress, notes } = req.body;

            // التحقق من البيانات المطلوبة
            if (!deliveryAddress) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'عنوان التوصيل مطلوب',
                    data: null
                });
            }
            console.log('customerId:', customerId);
            // جلب محتويات السلة
            const cartData = await CartService.getCart(customerId);
            console.log('cartData:', JSON.stringify(cartData, null, 2));

            if (!cartData.cartItems || cartData.cartItems.length === 0) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'سلة التسوق فارغة',
                    data: null
                });
            }

            // التحقق من توفر جميع المنتجات
            for (const item of cartData.cartItems) {
                if (item.product.quantity < item.quantity) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `المنتج "${item.product.name}" غير متوفر بالكمية المطلوبة. المتاح: ${item.product.quantity}`,
                        data: {
                            productId: item.product.id,
                            availableQuantity: item.product.quantity,
                            requestedQuantity: item.quantity
                        }
                    });
                }
            }
            // إنشاء طلب منفصل لكل متجر
            const createdOrders = [];

        
            // إنشاء إشعار للعميل
            await Notification.create({
                type: 'order',
                title: 'تأكيد الطلب',
                message: `تم إنشاء ${createdOrders.length} طلب بنجاح بإجمالي ${cartData.summary.totalPrice} ريال`,
                targetType: 'customer',
                targetId: customerId,
                relatedId: createdOrders[0].orderId,
                isRead: false
            }, { transaction });

            // مسح السلة
            await CartService.clearCart(customerId);

            await transaction.commit();

            res.status(201).json({
                success: true,
                message: `تم إنشاء ${createdOrders.length} طلب بنجاح`,
                data: {
                    orders: createdOrders,
                    summary: {
                        totalOrders: createdOrders.length,
                        totalAmount: cartData.summary.totalPrice,
                        totalItems: cartData.summary.totalItems,
                        deliveryAddress: deliveryAddress
                    }
                }
            });

        } catch (error) {
            await transaction.rollback();
            console.error('خطأ في معالجة الطلب:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في معالجة الطلب',
                data: null
            });
        }
    }
    

    async processAddCheckout(req, res) {
        const transaction = await sequelize.transaction();
       
        try {
            const customerId = req.customer?.id;
            const customer = await Customer.findByPk(customerId);
            if (customer.address === null || customer.city === null || customer.rating === null) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'يرجى اضافة عنوان توصيل',
                    data: null
                });
            }
            const  items  = req.body;
            console.log(items);

            if (!Array.isArray(items) || items.length === 0) {
                console.log('المشتريات مطلوبة');
                return res.status(400).json({
                    success: false,
                    message: 'المشتريات مطلوبة',
                    data: null
                });
            }

            let totalPrice = 0;
            const orderDetails = [];

            for (const item of items) {
                // تحقق من وجود المنتج فعلياً في قاعدة البيانات
                const product = await Product.findByPk(item.id);

                if (!product) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `المنتج بالمعرف ${item.id} غير موجود`,
                        data: { productId: item.id }
                    });
                }

                // تحقق من توفر الكمية
                if (product.quantity < item.quntity) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `الكمية غير متوفرة للمنتج ${product.name}`,
                        data: {
                            productId: product.id,
                            available: product.quantity,
                            requested: item.quntity
                        }
                    });
                }

                const itemTotal = parseFloat(item.Price) * parseInt(item.quntity);
                totalPrice += itemTotal;

                orderDetails.push({
                    productId: product.id,
                    quantity: item.quntity,
                    totalPrice: itemTotal,
                    notes : item.notes
                });
    /*
                // يمكنك إنقاص الكمية إذا رغبت
                await Product.update(
                    { quantity: product.quantity - item.quntity },
                    { where: { id: product.id }, transaction }
                );*/
            }

            // إنشاء الطلب الرئيسي
            const order = await Order.create({
                customerId,
                totalPrice,
                status: 'pending',
                deliveryAddress :customer.address,
                notes: null
            }, { transaction });

            // ربط التفاصيل بالطلب
            const fullDetails = orderDetails.map(detail => ({
                ...detail,
                orderId: order.id
            }));

            await OrderDetail.bulkCreate(fullDetails, { transaction });

            // إشعار العميل
            await Notification.create({
                type: 'order',
                title: 'تم استلام الطلب',
                message: `تم إنشاء طلبك رقم #${order.id} بنجاح.`,
                targetType: 'customer',
                targetId: customerId,
                relatedId: order.id,
                isRead: false
            }, { transaction });

           
           
            // إرسال إشعارات للمديرين باستخدام الخدمة المحسنة
            try {
                const FirebaseMessagingService = require('../services/FirebaseMessagingService');

                const notification = {
                    title: 'طلب جديد',
                    body: `تم تسجيل طلب جديد من ${customer.name} برقم #${order.id}`
                };

                const data = {
                    orderId: order.id.toString(),
                    customerName: customer.name,
                    clickAction: `/admin/orders/${order.id}`,
                    type: 'new_order'
                };

                const result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
                console.log(`📊 Notification results: ${result.successCount} successful, ${result.failureCount} failed`);

            } catch (notificationError) {
                console.error('❌ Failed to send admin notifications:', notificationError.message);
                // لا نوقف العملية إذا فشل الإشعار
            }
                    


            await transaction.commit();

            return res.status(201).json({
                success: true,
                message: 'تم إنشاء الطلب بنجاح',
                data: {
                    orderId: order.id,
                    status: order.status,
                    totalAmount: totalPrice,
                    totalItems: orderDetails.length
                }
            });

        } catch (error) {
            await transaction.rollback();
            console.error('خطأ في معالجة الطلب:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ أثناء تنفيذ الطلب',
                data: null
            });
        }
    }

     async getOrders(req, res) {
        try {
            const customerId = req.customer?.id;
           
            const {
                page = 1,
                limit = 10,
                status = null,
                startDate = null,
                endDate = null
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = { customerId: customerId };

            // فلتر بالحالة
            if (status) {
                whereClause.status = status;
            }

            // فلتر بالتاريخ
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
                if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
            }

            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                attributes: ['id', 'customerId','totalPrice','status', 'deliveryAddress','rating','createdAt'],
                include: [
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [
                            {
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price'],
                                include: [
                                    {
                                        model: Category,
                                        as: 'category',
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            // تنسيق البيانات
            const formattedOrders = orders.map(order => ({
                id: order.id,
                totalPrice: parseFloat(order.totalPrice),
                status: order.status,
                address: order.deliveryAddress,
                createdAt: order.createdAt,
                ProductsOrder: order.orderDetails ? order.orderDetails.map(detail => ({
                    id: detail.id,
                    name: detail.product.name,
                    category: detail.product.category ? detail.product.category.name : null,
                    quantity: detail.quantity,
                    price: parseFloat(detail.product.price),
                    totalPrice: parseFloat(detail.totalPrice),
                    notes: detail.notes,
                })) : [],
               // itemsCount: order.orderDetails ? order.orderDetails.length : 0
            }));

            res.json({
                success: true,
                message: 'تم جلب الطلبات بنجاح',
                data: {
                    orders: formattedOrders,
                   /* pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب الطلبات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getDelivery(req, res) {
    try {
        
        const driverId = req.driver.id;
        const deliveries = await Delivery.findAll({
            where: { deliveryPersonId: driverId },
            attributes: ['id', 'orderId'],
            include: [
                {
                    model: Order,
                    as: 'order',
                    attributes: ['totalPrice', 'status', 'deliveryAddress', 'notes','createdAt'],
                    include: [
                        {
                            model: OrderDetail,
                            as: 'orderDetails',
                            attributes: ['quantity', 'totalPrice', 'notes'],
                            include: [
                                {
                                    model: Product,
                                    as: 'product',
                                    attributes: ['id', 'name', 'price'],
                                    include: [
                                        {
                                            model: Category,
                                            as: 'category',
                                            attributes: ['id', 'name']
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            model: Customer,
                            as: 'customer',
                            attributes: ['id', 'name', 'phoneNumber', 'latitude', 'longitude']
                        }
                    ]
                }
            ]
        });

        const formatted = deliveries.map(delivery => ({
            id: delivery.orderId,
            totalPrice: delivery.order.totalPrice,
            status: delivery.order.status,
            address: delivery.order.deliveryAddress,
            createdAt: delivery.order.createdAt,
            customer: {
                id: delivery.order.customer.id,
                name: delivery.order.customer.name,
                phoneNumber: delivery.order.customer.phoneNumber,
                latitude: delivery.order.customer.latitude,
                longitude: delivery.order.customer.longitude
            },
            ProductsOrder: delivery.order.orderDetails.map(detail => ({
                id: detail.product.id,
                name: detail.product.name,
                category: detail.product.category.name,
                quantity: detail.quantity,
                price: detail.product.price,
                totalPrice: detail.totalPrice,
                notes: detail.notes
            }))
        }));

        res.json({
            success: true,
            message: 'تم جلب الطلبات المعلقة بنجاح',
            data: formatted
        });
    } catch (error) {
        console.error('خطأ في جلب الطلبات المعلقة:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم',
            data: null
        });
    }
}


    async getDeliveryDetails(req, res) {
        try {
            const driverId = req.driver.id;
            const deliveryId = req.params.id;         
            const delivery = await Delivery.findAll({
                where: { deliveryPersonId: driverId, id: deliveryId },
                attributes:['id','orderId']
                ,include: [
                    {
                        model: Order,
                        as: 'order',
                        attributes: ['totalPrice', 'status','deliveryAddress','notes'],
                        include: [
                            {
                                model: OrderDetail,
                                as: 'orderDetails',
                                attributes: ['quantity', 'totalPrice','notes'],
                                include: [
                                    {
                                        model: Product,
                                        as: 'product',
                                        attributes: ['id', 'name', 'price'],
                                        include: [
                                            {
                                                model: Category,
                                                as: 'category',
                                                attributes: ['id', 'name']
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                model: Customer,
                                as: 'customer',
                                attributes: ['id', 'name','phoneNumber','latitude','longitude']
                            }
                        ]
                    }
                ]
            });
                        
            res.json({
                success: true,
                message: 'تم جلب تفاصيل الطلب بنجاح',
                data: delivery
            });
        } catch (error) {
            console.error('خطأ في جلب تفاصيل الطلب:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async getProfileDriver(req, res) {
        try {
            const driverId = req.driver.id;

            const driver = await DeliveryPerson.findByPk(driverId, {
                attributes: { exclude: ['password'] }
            });

            if (!driver) {
                return res.status(404).json({
                    success: false,
                    message: 'السائق غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: { driver }
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات السائق:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async changePasswordDriver(req, res) {
            try {
                const driverId = req.driver.id;
                const { currentPassword, newPassword } = req.body;
    
                if (!currentPassword || !newPassword) {
                    return res.status(400).json({
                        success: false,
                        message: 'كلمة المرور الحالية والجديدة مطلوبتان',
                        data: null
                    });
                }
    
                const driver = await DeliveryPerson.findByPk(driverId);
                if (!driver) {
                    return res.status(404).json({
                        success: false,
                        message: 'العميل غير موجود',
                        data: null
                    });
                }
    
                // التحقق من كلمة المرور الحالية
                const isValidPassword = await bcrypt.compare(currentPassword, driver.password);
                if (!isValidPassword) {
                    return res.status(401).json({
                        success: false,
                        message: 'كلمة المرور الحالية غير صحيحة',
                        data: null
                    });
                }
    
                // تشفير كلمة المرور الجديدة
                const hashedPassword = await bcrypt.hash(newPassword, 10);
    
                // تحديث كلمة المرور
                await driver.update({ password: hashedPassword });
    
                res.json({
                    success: true,
                    message: 'تم تغيير كلمة المرور بنجاح',
                    data: null
                });
    
            } catch (error) {
                console.error('خطأ في تغيير كلمة المرور:', error);
                res.status(500).json({
                    success: false,
                    message: 'حدث خطأ في الخادم',
                    data: null
                });
            }
    }

    async completedorders(req, res) {
        try {
            const { id } = req.params;
            console.log(id);
            const order = await Order.findByPk(id);
            if (!order) {
                console.log('الطلب غير موجود');
                  return res.status(401).json({
                        success: false,
                        message: 'الطلب غير موجود',
                        data: null
                    });
            }else if (['processing', 'pending', 'rejected', ].includes(order.status)) {
                console.log('لا يمكن تغيير حالة الطلب');
                  return res.status(401).json({
                        success: false,
                        message: 'لا يمكن تغيير حالة الطلب',
                        data: null
                    });
            }
            await order.update({ status : 'completed' });
            console.log('تم تغيير حالة الطلب بنجاح');
            res.json({
                    success: true,
                    message: 'تم تغيير حالة الطلب بنجاح',
                    data: null
            });
            } catch (error) {
                console.error('حدث خطأ في الخادم', error);
                res.status(500).json({
                            success: false,
                            message: 'حدث خطأ في الخادم',
                            data: null
                });
        }
  }
}

module.exports = new CustomersController();
 