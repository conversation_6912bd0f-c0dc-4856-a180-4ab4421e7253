const { Admin, Customer, Category, Order, OrderDetail, Product, DeliveryPerson, AdminToken, sequelize} = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { executeWithRetry } = require('../utils/databaseUtils');
const path = require('path');

class AdminController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders,orderspending, products, deliveryPeople] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        Product.count(),
        DeliveryPerson.count()
      ]);

      res.render('admin/dashboard', {
        customers,
        admin,
        categories,
        orders,
        orderspending,
        products,
        deliveryPeople
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

  async AdminToken(req, res) {
    console.log('admintoken', req.body);
    const { token } = req.body;
    const adminId = req.session?.adminId || req.admin?.id;

    if (!token || !adminId) {
      return res.status(400).json({ success: false, message: 'توكن أو معرف الأدمن مفقود' });
    }

    try {
      // حذف التوكنات القديمة لنفس المدير
      await AdminToken.destroy({ where: { adminId } });

      // إنشاء توكن جديد
      await AdminToken.create({ token, adminId });

      console.log(`✅ تم حفظ توكن جديد للمدير ${adminId}: ${token.substring(0, 20)}...`);
      res.json({ success: true, message: 'تم حفظ توكن الإشعارات بنجاح' });
    } catch (err) {
      console.error('❌ خطأ في حفظ التوكن:', err);
      res.status(500).json({ success: false, message: 'خطأ في حفظ التوكن' });
    }
  }

  async testNotification(req, res) {
    try {
      const FirebaseMessagingService = require('../services/FirebaseMessagingService');

      const testNotification = {
        title: '🧪 إشعار تجريبي',
        body: `تم إرسال هذا الإشعار في ${new Date().toLocaleString('ar-EG')}`
      };

      const testData = {
        type: 'test',
        timestamp: Date.now().toString(),
        clickAction: '/admin/dashboard'
      };

      console.log('🧪 إرسال إشعار تجريبي...');
      const result = await FirebaseMessagingService.sendToAllAdmins(testNotification, testData);

      console.log(`📊 نتائج الإشعار التجريبي: ${result.successCount} نجح، ${result.failureCount} فشل`);

      if (result.successCount > 0) {
        res.json({
          success: true,
          message: 'تم إرسال الإشعار التجريبي بنجاح',
          results: {
            successCount: result.successCount,
            failureCount: result.failureCount
          }
        });
      } else {
        res.status(400).json({
          success: false,
          message: 'فشل في إرسال الإشعار التجريبي',
          results: {
            successCount: result.successCount,
            failureCount: result.failureCount,
            errors: result.results.filter(r => !r.success).map(r => r.error)
          }
        });
      }
    } catch (error) {
      console.error('❌ خطأ في إرسال الإشعار التجريبي:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في إرسال الإشعار التجريبي: ' + error.message
      });
    }
  }
}

module.exports = new AdminController();
