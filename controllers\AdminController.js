const { Admin, Customer, Category, Order, Product, DeliveryPerson, AdminToken } = require('../models');

class AdminController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders,orderspending, products, deliveryPeople] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Order.count({
          where: {
            status: 'pending'
          }
        }),
        Product.count(),
        DeliveryPerson.count()
      ]);

      res.render('admin/dashboard', {
        customers,
        admin,
        categories,
        orders,
        orderspending,
        products,
        deliveryPeople
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

  async AdminToken(req, res) {
    const { token } = req.body;
    const adminId = req.session?.adminId || req.admin?.id;

    if (!token || !adminId) {
      return res.status(400).json({ success: false, message: 'توكن أو معرف الأدمن مفقود' });
    }

    try {
      // حذف التوكنات القديمة لنفس المدير
      await AdminToken.destroy({ where: { adminId } });

      // إنشاء توكن جديد
      await AdminToken.create({ token, adminId });

      res.json({ success: true, message: 'تم حفظ توكن الإشعارات بنجاح' });
    } catch (err) {
      console.error('❌ خطأ في حفظ التوكن:', err);
      res.status(500).json({ success: false, message: 'خطأ في حفظ التوكن' });
    }
  }

  // إرسال إشعار مخصص
  async sendCustomNotification(req, res) {
    try {
      const { title, message, target, priority } = req.body;

      if (!title || !message) {
        return res.status(400).json({
          success: false,
          message: 'العنوان والرسالة مطلوبان'
        });
      }

      const FirebaseMessagingService = require('../services/FirebaseMessagingService');
      const NotificationService = require('../services/NotificationService');

      const notification = {
        title: title,
        body: message
      };

      const data = {
        type: 'custom',
        priority: priority || 'normal',
        timestamp: Date.now().toString(),
        clickAction: '/admin/dashboard'
      };

      let result = { successCount: 0, failureCount: 0 };

      // إرسال الإشعار حسب المستهدفين
      switch (target) {
        case 'all_admins':
          result = await FirebaseMessagingService.sendToAllAdmins(notification, data);
          break;
        case 'all_customers':
          await NotificationService.notifyAllCustomers({
            title: title,
            message: message,
            type: 'info',
            priority: priority || 'normal'
          });
          result.successCount = 1; // تقدير
          break;
        case 'all':
          const [adminResult] = await Promise.all([
            FirebaseMessagingService.sendToAllAdmins(notification, data),
            NotificationService.notifyAll({
              title: title,
              message: message,
              type: 'info',
              priority: priority || 'normal'
            })
          ]);
          result = adminResult;
          result.successCount += 1; // إضافة العملاء
          break;
        default:
          return res.status(400).json({
            success: false,
            message: 'مستهدف غير صحيح'
          });
      }


      res.json({
        success: true,
        message: 'تم إرسال الإشعار بنجاح',
        sentCount: result.successCount
      });

    } catch (error) {
      console.error('❌ Error sending custom notification:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في إرسال الإشعار: ' + error.message
      });
    }
  }

  // تحديد إشعار كمقروء
  async markNotificationAsRead(req, res) {
    try {
      const { id } = req.params;
      const Notification = require('../models').Notification;

      await Notification.update(
        { readAt: new Date() },
        { where: { id: id } }
      );

      res.json({ success: true });
    } catch (error) {
      console.error('❌ Error marking notification as read:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في تحديث الإشعار'
      });
    }
  }

  // حذف إشعار
  async deleteNotification(req, res) {
    try {
      const { id } = req.params;
      const Notification = require('../models').Notification;

      await Notification.destroy({ where: { id: id } });

      res.json({ success: true });
    } catch (error) {
      console.error('❌ Error deleting notification:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في حذف الإشعار'
      });
    }
  }

  // تحديد جميع الإشعارات كمقروءة
  async markAllNotificationsAsRead(req, res) {
    try {
      const Notification = require('../models').Notification;

      await Notification.update(
        { readAt: new Date() },
        { where: { readAt: null } }
      );

      res.json({ success: true });
    } catch (error) {
      console.error('❌ Error marking all notifications as read:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في تحديث الإشعارات'
      });
    }
  }

  // مسح جميع الإشعارات
  async clearAllNotifications(req, res) {
    try {
      const Notification = require('../models').Notification;

      await Notification.destroy({ where: {} });

      res.json({ success: true });
    } catch (error) {
      console.error('❌ Error clearing all notifications:', error);
      res.status(500).json({
        success: false,
        message: 'خطأ في حذف الإشعارات'
      });
    }
  }
}

module.exports = new AdminController();
